---
version: '3'


#- goreleaser check
#- goreleaser build
#  goreleaser announce - Announces a previously prepared release
#  goreleaser build - Builds the current project
#  goreleaser changelog - Preview your changelog
#  goreleaser check - Checks if configuration is valid
#  goreleaser completion - Generate the autocompletion script for the specified shell
#  goreleaser continue - Continues a previously prepared release
#  goreleaser healthcheck - Checks if needed tools are installed
#  goreleaser init - Generates a .goreleaser.yaml file
#  goreleaser jsonschema - Outputs goreleaser's JSON schema
#  goreleaser mcp - Start a MCP server that provides GoReleaser tools
#  goreleaser publish - Publishes a previously prepared release
#  goreleaser release - Releases the current project
#  goreleaser subscribe - Subscribe to GoReleaser Pro, or manage your subscription
#  goreleaser verify-license - Verify if the given license is valid


tasks:

  run:*:
    vars:
      SERVICE: '{{index .MATCH 0}}'
    requires:
      vars:
        - name: SERVICE
          enum:
            - init # 初始化配置文件
            - healthcheck # 环境检查
            - check # 配置验证
            - changelog # 生成变更日志
            - continue # 继续中断的发布
            - publish # 发布已构建的版本
            - announce # 发布通知
            - jsonschema # 生成JSON Schema
    cmds:
      - echo "Starting {{.SERVICE}}"
      - ak alfred {{.SERVICE}}

  # 仅构建二进制
  build:
    desc: Build binaries without publishing
    cmds:
      - goreleaser build --snapshot --rm-dist

  # 完整发布流程
  release:
    desc: Full release workflow (build + publish)
    cmds:
      - task: run:healthcheck
      - task: run:check
      - goreleaser release --rm-dist
      - task: run:announce

  # 默认任务：完整发布流程 (commented out to prevent auto-execution when included)
  # default:
  #   desc: Run full release process
  #   cmds:
  #     - task: release
