version: '3'

includes:
  lint: ./Taskfile.lint.yml
  sk:
    taskfile: ./secrets/Taskfile.yml
    dir: ./secrets


# Nix-Darwin management tasks
# Converted from rich-demo Justfile with additional migration functionality

vars:
  NIX_DIR: './nix'
  HOSTNAME:
    sh: scutil --get ComputerName | tr ' ' '-' | tr '[:upper:]' '[:lower:]'
  SYSTEM:
    sh: |
      if [[ $(uname -m) == "arm64" ]]; then
        echo "aarch64-darwin"
      else
        echo "x86_64-darwin"
      fi
  OS:
    sh: uname
  USERNAME:
    sh: whoami
  # Default cleanup days for maintenance tasks
  CLEANUP_DAYS: '{{.CLEANUP_DAYS | default "7"}}'
  # Nix store path
  NIX_STORE_PATH: '/nix/store'
  # System profile path
  SYSTEM_PROFILE: '/nix/var/nix/profiles/system'
  # GitHub token pulled from gh CLI for GitHub API rate limits
  GITHUB_TOKEN:
    sh: gh auth token 2>/dev/null || echo ""
  # Dynamic rebuild command based on OS
  REBUILD_CMD:
    sh: |
      if [[ "{{.OS}}" == "Darwin" ]]; then
        echo "darwin-rebuild"
      else
        echo "nixos-rebuild"
      fi
  # Dynamic build target based on OS
  BUILD_TARGET:
    sh: |
      if [[ "{{.OS}}" == "Darwin" ]]; then
        echo "darwinConfigurations.{{.HOSTNAME}}.system"
      else
        echo "nixosConfigurations.{{.HOSTNAME}}.config.system.build.toplevel"
      fi

env:
  NIX_CONFIG: 'access-tokens = github.com={{.GITHUB_TOKEN}}'


tasks:
  default:
    desc: "构建并切换到当前系统配置"
    cmds:
      - task: build-switch

  # === 核心构建任务 ===
  build:
    desc: "构建系统配置（不切换）"
    cmds:
      - echo "正在为 {{.HOSTNAME}} ({{.SYSTEM}}) 构建 {{.OS}} 配置..."
      - nix build ./nix#{{.BUILD_TARGET}} --extra-experimental-features 'nix-command flakes'
      - echo "构建完成！"

  build-switch:
    desc: "构建并切换到新配置"
    cmds:
      - echo "正在构建并切换 {{.OS}} 配置..."
      - sudo {{.REBUILD_CMD}} switch --flake ./nix#{{.HOSTNAME}}
      - echo "切换到新配置完成！"


  darwin:
    desc: "构建并切换到 nix-darwin 配置"
    cmds:
      - echo "🚀 正在为 {{.HOSTNAME}} ({{.SYSTEM}}) 构建 nix-darwin 配置..."
      - nix build ./nix#{{.BUILD_TARGET}} --extra-experimental-features 'nix-command flakes'
      - echo "🔄 正在切换到新配置..."
      - sudo {{.REBUILD_CMD}} switch --flake ./nix#{{.HOSTNAME}}
      - echo "✅ nix-darwin 配置应用成功！"

  build-z: # 遇到 error: public key is not valid
    cmd: sudo darwin-rebuild switch --option substituters "https://cache.nixos.org/" --option trusted-public-keys "cache.nixos.org-1:6NCHdD59X431o0gWypbMrAURkbJ16ZPMQFGspcDShjY=" --flake .#luck

  # FIXME 其实可以直接使用 嵌套task，直接复用build就可以了，后面直接添加参数 --show-trace
  darwin-debug:
    desc: "使用详细输出构建并切换（用于调试）"
    cmds:
      - echo "🐛 正在使用调试输出构建 {{.OS}} 配置..."
      - nix build ./nix#{{.BUILD_TARGET}} --show-trace --verbose --extra-experimental-features 'nix-command flakes'
      - echo "🔄 正在使用调试输出切换..."
      - sudo {{.REBUILD_CMD}} switch --flake ./nix#{{.HOSTNAME}} --show-trace --verbose

  # Nix related commands
  update:
    desc: "更新所有 flake 到最新版本，并查看更新内容"
    cmds:
      # - task: upgrade
      - nix flake update # nix flake update --flake ./nix
      - nix flake show

  upgrade:
    cmds:
      - sudo nix upgrade-nix

  update-input:
    desc: "Update specific flake input (usage: task nix:update-input INPUT=nixpkgs)"
    cmds:
      - nix flake lock --update-input "{{.INPUT}}" ./nix
    requires:
      vars: [INPUT]

  history:
    desc: "List all generations of the system profile"
    cmds:
      - nix profile history --profile /nix/var/nix/profiles/system

  repl:
    desc: "Open a nix repl with the flake"
    cmds:
      - bash -lc 'cd nix && nix repl -f flake:nixpkgs'

  fmt:
    desc: "Format nix files in the repository"
    dir: './nix'
    cmds:
      - nix fmt

  check-config:
    desc: "Check nix-darwin configuration syntax"
    cmds:
      - echo "🔍 Checking configuration syntax..."
      - nix flake check ./nix
      - echo "✅ Configuration syntax is valid"


  prune:
    desc: clean + gc
    cmds:
      - task: clean
      - task: clean-home
      - task: gc


  clean:
    internal: true
    interactive: true
    silent: true
    desc: 清理 system generations，注意不会立即删除软件包本身
    cmds:
      - echo "🧹 Cleaning old system generations (older than {{.CLEANUP_DAYS}} days)..."
      - sudo nix profile wipe-history --profile "{{.SYSTEM_PROFILE}}" --older-than "{{.CLEANUP_DAYS}}d"
      - echo "✅ System generations cleaned"

  clean-home:
    internal: true
    interactive: true
    silent: true
    desc: 清理 home-manager generations，同样不会立即删除软件包本身
    cmds:
      - echo "🧹 Cleaning home-manager generations..."
      - nix profile wipe-history --profile ~/.local/state/nix/profiles/home-manager --older-than "{{.CLEANUP_DAYS}}d" 2>/dev/null || echo "No home-manager profile found"

  gc:
    internal: true
    interactive: true
    silent: true
    desc: 真正删除所有未被引用的软件包（store paths），释放磁盘空间。 # 只有被 profile 引用的包才会被认为是“使用中”
    cmds:
      - echo "🗑️  Running garbage collection (older than {{.CLEANUP_DAYS}} days)..."
      # - sudo nix-collect-garbage --delete-older-than "{{.CLEANUP_DAYS}}d"
      - nix-collect-garbage --delete-older-than "{{.CLEANUP_DAYS}}d"
      - echo "✅ Garbage collection completed"


  pin:
    cmds:
      - nix profile list # 已经 pin 的包
      # nix profile add nixpkgs#<package-name>

  syntax-verify:
    desc: 判断nix语法是否正确 # nix-instantiate --parse /Users/<USER>/Desktop/dotfiles/nix/home/<USER>/dev/null && echo "Bash.nix syntax is valid
    cmd: nix-instantiate --parse {{.CLI_ARGS}} > /dev/null && echo "Bash.nix syntax is valid


#  status:
#    desc: "Show comprehensive system status"
#    silent: true
#    cmds:
#      - task: info
#      - task: disk-usage
#      - echo "Recent system generations:"
#      - nix profile history --profile "{{.SYSTEM_PROFILE}}" | head -10 || echo "  Could not read system profile history"
