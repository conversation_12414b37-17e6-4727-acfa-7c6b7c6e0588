{"permissions": {"allow": ["Bash(tree:*)", "WebFetch(domain:nix-community.github.io)", "Bash(nix-instantiate:*)", "Bash(nix search:*)", "WebSearch", "WebFetch(domain:github.com)", "Bash(find:*)", "<PERSON><PERSON>(sudo:*)", "Bash(task nix:build-switch:*)", "Bash(git add:*)", "Bash(nix flake show:*)", "Bash(nix-store:*)", "Bash(nix:*)", "Read(//private/etc/rclone/r2/**)", "Read(//private/etc/ssh/github/**)", "Read(//Users/<USER>/.config/sops/age/**)", "Bash(sops:*)", "Read(//private/etc/claude/zai/**)", "<PERSON><PERSON>(launchctl list:*)", "Bash(SOPS_AGE_KEY_FILE=/Users/<USER>/.config/sops/age/keys.txt sops:*)", "Bash(export:*)", "Read(//nix/store/16h3sgd7yyirvrsx4c6dpzclsvic1002-sops-3.10.2/bin/**)", "Bash(systemctl:*)", "Read(//private/var/run/**)", "Read(//private/var/**)", "<PERSON><PERSON>(echo:*)", "Read(//nix/store/p81zwhia62i4iqiafm7bj3h4sjjdlc8d-etc/etc/claude/zai/**)", "<PERSON><PERSON>(cat:*)", "Read(//nix/store/p81zwhia62i4iqiafm7bj3h4sjjdlc8d-etc/etc/claude/**)", "Read(//Users/<USER>/.nix-profile/**)", "Read(//nix/store/2g1vpgvaa6x00p51ylsg5bzar89hl30q-system-path/bin/**)", "<PERSON><PERSON>(claude:*)", "Read(//Users/<USER>/**)", "Read(//Users/<USER>/.config/**)", "Bash(uvx:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(launchctl:*)", "<PERSON><PERSON>(task:*)", "<PERSON><PERSON>(test:*)", "Bash(rclone lsl:*)", "Bash(pnpm:*)", "Read(//nix/store/**)", "<PERSON><PERSON>(home-manager:*)", "Read(//etc/**)", "Bash(readlink:*)", "mcp__deep<PERSON><PERSON>__ask_question", "<PERSON><PERSON>(@code-reviewer)", "Read(//nix/var/nix/profiles/**)", "Bash(scutil:*)", "mcp__octocode__githubSearchCode", "mcp__octocode__githubSearchRepositories", "mcp__octocode__githubViewRepoStructure", "mcp__octocode__githubGetFileContent", "mcp__ddg__duckduckgo_web_search", "mcp__nixos-mcp__nixos_search", "mcp__nixos-mcp__home_manager_search", "mcp__filesystem__list_allowed_directories"], "deny": [], "ask": []}}