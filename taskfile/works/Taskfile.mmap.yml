---
version: '3'

vars:
  MMAP: mmap

tasks:
  # 1. 主机发现
  host-discovery:
    desc: 主机发现相关任务
    cmds:
      - echo "选择主机发现方式:"
      - echo "1. ping-scan - ICMP ping扫描"
      - echo "2. arp-scan - ARP扫描(局域网)"
      - echo "3. no-ping - 跳过主机发现直接扫描"
      - echo "使用: task mmap:host-discovery ping-scan -- <target>"

  ping-scan:
    desc: ICMP ping扫描发现主机
    cmds:
      - "{{.MMAP}} -sn {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:ping-scan -- ***********/24"

  arp-scan:
    desc: ARP扫描(局域网专用)
    cmds:
      - "{{.MMAP}} -PR {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:arp-scan -- ***********/24"

  no-ping:
    desc: 跳过主机发现直接扫描
    cmds:
      - "{{.MMAP}} -Pn {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:no-ping -- ***********"

  # 2. 端口扫描
  port-scan:
    desc: 端口扫描相关任务
    cmds:
      - echo "选择端口扫描方式:"
      - echo "1. syn-scan - SYN半开扫描"
      - echo "2. tcp-scan - TCP全连接扫描"
      - echo "3. udp-scan - UDP端口扫描"
      - echo "4. port-range - 指定端口范围扫描"
      - echo "使用: task mmap:port-scan syn-scan -- <target>"

  syn-scan:
    desc: SYN半开扫描（默认）
    cmds:
      - "{{.MMAP}} -sS {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:syn-scan -- ***********"

  tcp-scan:
    desc: TCP全连接扫描
    cmds:
      - "{{.MMAP}} -sT {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:tcp-scan -- ***********"

  udp-scan:
    desc: UDP端口扫描
    cmds:
      - "{{.MMAP}} -sU {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:udp-scan -- ***********"

  port-range:
    desc: 指定端口范围扫描
    cmds:
      - "{{.MMAP}} -p {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供端口和目标地址，例如: task mmap:port-range -- 80,443,22 ***********"

  # 3. 服务与系统识别
  fingerprint:
    desc: 服务与系统识别相关任务
    cmds:
      - echo "选择识别方式:"
      - echo "1. version-detect - 服务版本探测"
      - echo "2. os-detect - 操作系统检测"
      - echo "3. aggressive - 综合扫描模式"
      - echo "使用: task mmap:fingerprint version-detect -- <target>"

  version-detect:
    desc: 服务版本探测
    cmds:
      - "{{.MMAP}} -sV {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:version-detect -- ***********"

  os-detect:
    desc: 操作系统检测
    cmds:
      - "{{.MMAP}} -O {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:os-detect -- ***********"

  aggressive:
    desc: 综合扫描模式（OS检测+版本探测+脚本扫描）
    cmds:
      - "{{.MMAP}} -A {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:aggressive -- ***********"

  # 4. 脚本扫描（NSE）
  script-scan:
    desc: 脚本扫描相关任务
    cmds:
      - echo "选择脚本扫描方式:"
      - echo "1. default-script - 默认安全脚本扫描"
      - echo "2. vuln-scan - 漏洞检测脚本"
      - echo "3. ssl-cert - TLS证书信息提取"
      - echo "使用: task mmap:script-scan default-script -- <target>"

  default-script:
    desc: 默认安全脚本扫描
    cmds:
      - "{{.MMAP}} --script default {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:default-script -- ***********"

  vuln-scan:
    desc: 漏洞检测脚本
    cmds:
      - "{{.MMAP}} --script vuln {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:vuln-scan -- ***********"

  ssl-cert:
    desc: TLS证书信息提取
    cmds:
      - "{{.MMAP}} --script ssl-cert {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:ssl-cert -- ***********"

  # 5. 输出与性能优化
  output-options:
    desc: 输出与性能优化相关任务
    cmds:
      - echo "选择输出方式:"
      - echo "1. output-normal - 保存结果到文本文件"
      - echo "2. output-xml - 保存结果到XML文件"
      - echo "3. fast-scan - 加速扫描"
      - echo "使用: task mmap:output-options output-normal -- <target>"

  output-normal:
    desc: 保存结果到文本文件
    cmds:
      - "{{.MMAP}} -oN result.txt {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:output-normal -- ***********"

  output-xml:
    desc: 保存结果到XML文件
    cmds:
      - "{{.MMAP}} -oX result.xml {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:output-xml -- ***********"

  fast-scan:
    desc: 加速扫描（T4速度）
    cmds:
      - "{{.MMAP}} -T4 {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:fast-scan -- ***********"

  # 6. 隐蔽扫描技巧
  stealth-scan:
    desc: 隐蔽扫描技巧相关任务
    cmds:
      - echo "选择隐蔽扫描方式:"
      - echo "1. fragment - 分片数据包发送"
      - echo "2. decoy - 诱饵IP混淆来源"
      - echo "3. source-port - 伪造源端口"
      - echo "使用: task mmap:stealth-scan fragment -- <target>"

  fragment:
    desc: 分片数据包发送
    cmds:
      - "{{.MMAP}} -f {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:fragment -- ***********"

  decoy:
    desc: 诱饵IP混淆来源
    cmds:
      - "{{.MMAP}} -D {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供诱饵IP和目标地址，例如: task mmap:decoy -- ***********,*********** ***********"

  source-port:
    desc: 伪造源端口
    cmds:
      - "{{.MMAP}} --source-port 53 {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标地址，例如: task mmap:source-port -- ***********"

  # 实用组合命令
  quick-web-scan:
    desc: 快速扫描网段存活主机并探测Web服务
    cmds:
      - echo "快速扫描网段存活主机并探测Web服务"
      - echo "使用示例: task mmap:quick-web-scan -- ***********/24"
      - "{{.MMAP}} -sn {{.CLI_ARGS}} && {{.MMAP}} -sV -p 80,443 {{.CLI_ARGS}}"
    preconditions:
      - sh: test -n "{{.CLI_ARGS}}"
        msg: "请提供目标网段，例如: task mmap:quick-web-scan -- ***********/24"
