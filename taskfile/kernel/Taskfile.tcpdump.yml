---
version: '3'


#- tcpdump -i eth0 -vnn host <host> # 抓取包含************的数据包  tcpdump -i eth0 -vnn host ************
#- tcpdump -i eth0 -vnn net <segment> # 抓取包含**********/24网段的数据包  tcpdump -i eth0 -vnn net **********/24
#- tcpdump -i eth0 -vnn port <port> # 抓取包含端口22的数据包  tcpdump -i eth0 -vnn port 22
#- tcpdump -i eth0 -vnn <protocol> # 抓取指定协议的数据包，protocol可以设置为udp, icmp, arp, ip
#- tcpdump -i eth0 -vnn src host ************ # 抓取源ip是************数据包
#- tcpdump -i eth0 -vnn dst host ************ # 抓取目的ip是************数据包
#- tcpdump -i eth0 -A 'tcp && port 80



tasks:
  info:
    cmd: wrangler whoami # 无法推送到pages，怎么排查具体问题? 怎么查看wrangler权限设置?
