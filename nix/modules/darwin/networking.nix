# Darwin networking configuration
# macOS-specific network settings and optimizations
{...}: {
  # Basic networking configuration
  # Note: macOS networking is mostly managed through System Preferences
  # These are the available nix-darwin networking options

  # macOS-specific network optimizations
  # Most network settings are managed through System Preferences
  # Network configuration (will be overridden by host-specific settings)


  networking = {
    hostName = "luck";
    computerName = "luck";
    localHostName = "luck";

    # DNS configuration
    dns = [
      "*******" # Cloudflare
      "*******" # Cloudflare secondary
      "*******" # Google
      "*******" # Google secondary
    ];

    # DNS search domains
    search = ["home" "local" "lan"];

    wakeOnLan = {
      enable = false;
    };
    applicationFirewall = {
      enable = true;
      allowSigned = true;
      enableStealthMode = true;
      blockIncoming = true;
    };
  };

  # Network services configuration
  services = {
    # Network time synchronization
    ntp = {
      enable = true;
      servers = [
        "time.apple.com"
        "time.google.com"
        "time.cloudflare.com"
      ];
    };

    # Enable SSH daemon
    openssh = {
      enable = true;
      settings = {
        PermitRootLogin = "no";
        PasswordAuthentication = "no";
        X11Forwarding = false;
        AllowTcpForwarding = true;
        GatewayPorts = "clientspecified";
        ClientAliveInterval = 60;
        ClientAliveCountMax = 3;
      };
    };

    # Enable Bonjour/mDNS
    bonjour.enable = true;

    # Enable Time Machine network discovery
    timemachine.enable = true;
  };

  # Network-related system preferences
  system.defaults = {
    # Network preferences
    ".GlobalPreferences" = {
      # Disable automatic proxy detection
      "com.apple.proxy.pac.enable" = false;
      "com.apple.proxy.pac.url" = "";
    };

    # Airport/Wi-Fi preferences
    "com.apple.airport" = {
      # Remember recent networks
      RememberRecentNetworks = true;

      # Automatically join preferred networks
      AutomaticallyJoinPreferredNetworks = true;

      # Require password for networks
      RequirePasswordForPersonalHotspot = true;
    };
  };

  # Custom network-related launchd services
  launchd.daemons = {
    # Network connectivity check service
    network-check = {
      serviceConfig = {
        Label = "local.network.check";
        ProgramArguments = [
          "/bin/bash"
          "-c"
          ''
            # Simple network connectivity check
            if ping -c 1 ******* >/dev/null 2>&1; then
              echo "$(date): Network connectivity OK" >> /var/log/network-check.log
            else
              echo "$(date): Network connectivity FAILED" >> /var/log/network-check.log
            fi
          ''
        ];
        StartInterval = 300; # Check every 5 minutes
        StandardOutPath = "/var/log/network-check.log";
        StandardErrorPath = "/var/log/network-check.log";
      };
    };

    # DNS cache flush service (useful for development)
    dns-cache-flush = {
      serviceConfig = {
        Label = "local.dns.cache.flush";
        ProgramArguments = [
          "/usr/bin/dscacheutil"
          "-flushcache"
        ];
        # This service can be triggered manually: sudo launchctl start local.dns.cache.flush
        RunAtLoad = false;
      };
    };

    # Network monitoring service
    network-monitor = {
      serviceConfig = {
        Label = "com.user.network.monitor";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # Network health monitoring script
            while true; do
              # Test network connectivity
              if ping -c 1 ******* > /dev/null 2>&1; then
                echo "$(date): Network is healthy" >> /var/log/network-monitor.log
              else
                echo "$(date): Network connectivity issue detected" >> /var/log/network-monitor.log
              fi
              sleep 300  # Check every 5 minutes
            done
          ''
        ];
        RunAtLoad = true;
        KeepAlive = true;
        StandardOutPath = "/var/log/network-monitor.log";
        StandardErrorPath = "/var/log/network-monitor.log";
      };
    };
  };

  # Network monitoring and diagnostics
  # Most network tools are already in home-manager configuration
}
