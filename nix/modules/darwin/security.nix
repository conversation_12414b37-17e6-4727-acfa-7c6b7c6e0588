# Darwin security configuration
{lib, ...}: {
  # PAM configuration
  security.pam = {
    services = {
      sudo_local = {
        enable = true;
        touchIdAuth = true;
        watchIdAuth = true;
      };
    };
  };

  security.sudo.extraConfig = ''
    # Environment variables to preserve
    Defaults env_keep += "EDITOR VISUAL PAGER"
    Defaults env_keep += "HOME XDG_*"
    Defaults env_keep += "SSH_AUTH_SOCK"
    Defaults env_keep += "KUBECONFIG"
    Defaults env_keep += "AWS_*"
    Defaults env_keep += "GOOGLE_*"
    Defaults env_keep += "DOCKER_*"

    # Security settings
    Defaults !tty_tickets
    Defaults !insults
    Defaults log_output
    Defaults loglinelen=0
    Defaults passwd_tries=3
    Defaults badpass_message="Password incorrect, please try again"
    Defaults passprompt="[sudo] password for %p: "
    Defaults secure_path="/run/current-system/sw/bin:/usr/bin:/bin"

    # Passwordless sudo for admin group (optional, uncomment if needed)
    # %admin ALL=(ALL) NOPASSWD: ALL
  '';

  security.pki = {
    installCACerts = true;
    certificateFiles = [
      # Add custom CA certificates here
      # ./certs/my-ca.crt
    ];
  };

  # System integrity protection settings
  system.defaults = {
    # Security and privacy settings
    ".GlobalPreferences" = {
      # Require password immediately after sleep or screen saver
      "com.apple.screensaver.askForPassword" = 1;
      "com.apple.screensaver.askForPasswordDelay" = 0;

      # Disable automatic login
      "com.apple.loginwindow.DisableConsoleAccess" = true;
      "com.apple.loginwindow.FastUserSwitchingEnabled" = false;

      # Disable remote login
      "com.apple.remoteloginenabled" = false;

      # Disable guest account
      "com.apple.loginwindow.GuestEnabled" = false;

      # Disable automatic file sharing
      "com.apple.smb.server.EnableAFP" = false;
      "com.apple.smb.server.EnableSMB" = false;
    };

    # Application firewall settings
    "com.apple.security.firewall" = {
      EnableFirewall = true;
      BlockAllIncoming = true;
      EnableStealthMode = true;
      AllowSigned = true;
      AllowSignedApp = true;
    };

    # FileVault encryption
    "com.apple.security.fvault" = {
      EnableFileVault = false; # Set to true to enable (requires user interaction)
      UseRecoveryKey = true;
      ShowRecoveryKey = false;
    };

    # Gatekeeper settings
    "com.apple.security.gatekeeper" = {
      EnableAssessment = true;
      AllowIdentifiedDevelopers = true;
      DisableOverride = false;
    };

    # Privacy settings
    "com.apple.security.privacy" = {
      # Disable location services for system
      "LocationServicesEnabled" = false;

      # Disable diagnostics and usage reporting
      "SubmitDiagnostics" = false;
      "SubmitUsageData" = false;

      # Disable personalized advertising
      "AllowApplePersonalizedAdvertising" = false;

      # Disable analytics
      "AnalyticsEnabled" = false;
    };

    # Bluetooth security
    "com.apple.bluetooth" = {
      "ControllerPowerState" = 0; # Bluetooth off by default
      "DiscoverableState" = 0; # Not discoverable
      "PanServices" = 0; # Disable PAN services
    };

    # Time Machine security
    "com.apple.timemachine" = {
      "MobileBackups" = false; # Disable local Time Machine snapshots
      "AutoBackup" = false; # Disable automatic backups
    };

    # Security-related system defaults
    screensaver = {
      askForPassword = true;
      askForPasswordDelay = 0;
    };

    loginwindow = {
      GuestEnabled = false;
      # DisableConsoleAccess = true;  # Conflicts with host-specific setting
      SHOWFULLNAME = true;
      # LoginwindowText = "Authorized users only";  # Conflicts with host-specific setting
    };

    # NSGlobalDomain screensaver settings are handled by screensaver section

    finder = {
      FXEnableExtensionChangeWarning = lib.mkDefault true;
      AppleShowAllExtensions = lib.mkDefault true;
      # WarnOnEmptyTrash = true;  # This option may not be available
    };

    dock = {
      show-recents = false;
    };
  };

  # Security services
  services = {
    # Enable audit daemon
    auditd.enable = true;

    # Enable firewall service
    firewall = {
      enable = true;
      allowedTCPPorts = [22 80 443];
      allowedUDPPorts = [];
      allowPing = true;
      logRefusedConnections = true;
      logRefusedPackets = true;
      logRefusedUnicastsOnly = false;
    };

    # Enable fail2ban
    fail2ban = {
      enable = true;
      jails = {
        sshd = {
          enabled = true;
          port = "ssh";
          filter = "sshd";
          maxretry = 3;
          bantime = "1h";
          findtime = "1h";
        };
      };
    };

    # Enable clam antivirus
    clamav = {
      daemon.enable = true;
      updater.enable = true;
    };
  };

  
  # Security-related launchd services
  launchd.daemons = {
    # Security monitoring service
    "security-monitor" = {
      serviceConfig = {
        Label = "com.user.security.monitor";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # Security monitoring script
            while true; do
              # Check for suspicious processes
              if ps aux | grep -i "keylogger\|spyware\|malware" | grep -v grep > /dev/null; then
                echo "$(date): Suspicious process detected" >> /var/log/security-monitor.log
              fi

              # Check for unusual network connections
              if netstat -an | grep -i "established" | grep -E ":[0-9]{4,5}" > /dev/null; then
                echo "$(date): Unusual network connection detected" >> /var/log/security-monitor.log
              fi

              # Check for unusual files in system directories
              if find /usr/bin /usr/sbin -type f -mtime -1 2>/dev/null | head -5 > /dev/null; then
                echo "$(date): New system files detected" >> /var/log/security-monitor.log
              fi

              sleep 300  # Check every 5 minutes
            done
          ''
        ];
        RunAtLoad = true;
        KeepAlive = true;
        StandardOutPath = "/var/log/security-monitor.log";
        StandardErrorPath = "/var/log/security-monitor.log";
      };
    };

    # Backup service (will be overridden by host-specific primaryUser)
    "security-backup" = {
      serviceConfig = {
        Label = "com.user.security.backup";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            PRIMARY_USER=$USER
            if [ -z "$PRIMARY_USER" ]; then
              PRIMARY_USER=$(whoami)
            fi
            rsync -av --delete \
              "/Users/<USER>/.ssh/" \
              "/Users/<USER>/.gnupg/" \
              "/Users/<USER>/.config/sops/" \
              "/Users/<USER>/backup/security/"
          ''
        ];
        StartCalendarInterval = [
          {
            Hour = 2;
            Minute = 0;
          }
        ];
        StandardOutPath = "/var/log/security-backup.log";
        StandardErrorPath = "/var/log/security-backup.log";
      };
    };
  };

  # Security-related system defaults
  system.activationScripts.postActivation.text = ''
    # Set security-related permissions
    chmod 700 /Users/<USER>/.{ssh,gnupg} 2>/dev/null || true
    chmod 600 /Users/<USER>/.ssh/* 2>/dev/null || true
    chmod 600 /Users/<USER>/.gnupg/* 2>/dev/null || true

    # Remove world-writable permissions from user directories
    find /Users -type d -perm -o+w -exec chmod o-w {} \; 2>/dev/null || true

    # Create secure tmp directories
    mkdir -p /tmp/secure
    chmod 700 /tmp/secure
  '';

  # Security-related environment variables
  environment.variables = {
    # GPG settings
    GPG_TTY = "$(tty)";
    GNUPGHOME = "$HOME/.gnupg";

    # SSH settings
    SSH_AUTH_SOCK = "$HOME/.ssh/ssh_auth_sock";

    # SOPS settings
    SOPS_AGE_KEY_FILE = "$HOME/.config/sops/age/keys.txt";

    # Security flags
    HISTCONTROL = "ignorespace:ignoredups";
    HISTIGNORE = "passwd *:password *:sudo -S *:su *:ssh *:gpg *:sops *";
  };
}
