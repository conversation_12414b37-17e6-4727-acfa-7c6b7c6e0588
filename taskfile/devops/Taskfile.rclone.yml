---
version: '3'

vars:
  RCLONE: "rclone"
  RCLONE_REMOTE: "r2"                     # rclone.conf 中的存储配置名



tasks:
  default:
    desc: 上传本地文件到远程
    cmds:
      - task: '{{.TASK}}'
    vars:
      TASK:
        sh: gum choose sync-docs-images bisync-scratches
        msg: 选择待上传的bucket

  sync:
    internal: true
    desc: rclone sync 操作
    cmds:
#      - '{{.RCLONE}} sync --update --checksum --fast-list --transfers 30 --s3-upload-concurrency 30 "{{.LOCAL_PATH}}" "{{.RCLONE_REMOTE}}:{{.BUCKET}}" --stats=10s --progress {{.DRY_RUN_FLAG}}'
      - '{{.RCLONE}} sync --update --checksum --fast-list --transfers 30 --s3-upload-concurrency 30 "{{.LOCAL_PATH}}" "{{.RCLONE_REMOTE}}:{{.BUCKET}}" --stats-one-line -v {{.DRY_RUN_FLAG}}'

  bisync:
    internal: true
    desc: rclone bisync 操作
    cmds:
      - '{{.RCLONE}} bisync "{{.LOCAL_PATH}}" "{{.RCLONE_REMOTE}}:{{.BUCKET}}" --create-empty-src-dirs --resync --stats-one-line -v {{.DRY_RUN_FLAG}}'
    requires:
      vars: [RCLONE, LOCAL_PATH, RCLONE_REMOTE, BUCKET, DRY_RUN_FLAG]


  check:
    internal: true
    desc: rclone check 操作
    cmds:
      - '{{.RCLONE}} check "{{.LOCAL_PATH}}" "{{.RCLONE_REMOTE}}:{{.BUCKET}}" --size-only --one-way {{.DRY_RUN_FLAG}}'
    requires:
      vars: [RCLONE, LOCAL_PATH, RCLONE_REMOTE, BUCKET, DRY_RUN_FLAG]


  sync-docs-images:
    desc: 同步 docs-images 到远程（用于 systemd）
    cmds:
      - task: sync
        vars:
          DRY_RUN_FLAG: ""
          BUCKET: "docs"
          LOCAL_PATH: "$HOME/Desktop/docs-images"
      - echo "Execute sync-docs task success"
    preconditions:
      - sh: test -d "$HOME/Desktop/docs-images/"
        msg: "Directory $HOME/Desktop/docs-images does not exist"


  bisync-scratches:
    desc: 双向同步 scratches 到 R2
    cmds:
      - task: bisync
        vars:
          LOCAL_PATH:
            sh: |
              jb="$HOME/Library/Application Support/JetBrains"
              ls -d "$jb"/*/scratches 2>/dev/null | head -n1
          DRY_RUN_FLAG: ""
          BUCKET: "scratches"
      - echo "Execute bisync-scratches task success"
    preconditions:
      - sh: test -d "$HOME/Library/Application Support/JetBrains"/*/scratches
        msg: "No JetBrains IDE scratches directory found"

  config:
    desc: 查看 rclone 相关配置
    interactive: true
    silent: true
    cmds:
      - echo "rclone 配置文件路径"
      - '{{.RCLONE}} config file'
      - echo "统计远程存储使用量"
      - '{{.RCLONE}} size {{.RCLONE_REMOTE}}:{{.BUCKET}}'
      - echo "列出所有配置的远程存储"
      - '{{.RCLONE}} listremotes'
      - echo "列出远程文件详情（含大小/时间）" # 列出指定路径下的所有的文件以及文件大小和路径，并且显示上传时间
      - '{{.RCLONE}} lsl {{.RCLONE_REMOTE}}:{{.BUCKET}}/{{.TARGET_PATH}}'


  rev-sync:
    alias: [download]
    internal: true
    desc: "⚠️ 同步远程文件到本地（会删除本地多余文件）"
    cmds:
      - '{{.RCLONE}} sync {{.RCLONE_REMOTE}}:{{.BUCKET}}/{{.REMOTE_PATH}} {{.LOCAL_PATH}} {{.DRY_RUN_FLAG}}'
    vars:
      REMOTE_PATH: "docs"  # 指定远程路径


  delete:
    internal: true
    desc: "🔥 删除远程文件夹内容（默认模拟执行）"
    cmds:
      - '{{.RCLONE}} delete {{.RCLONE_REMOTE}}:{{.BUCKET}}/{{.TARGET_PATH}} {{.DRY_RUN_FLAG}}'
    vars:
      TARGET_PATH: "x"  # 需删除的路径

  purge:
    internal: true
    desc: "🔥 彻底删除远程文件夹（包括目录本身）"
    cmds:
      - '{{.RCLONE}} purge {{.RCLONE_REMOTE}}:{{.BUCKET}}/{{.TARGET_PATH}} {{.DRY_RUN_FLAG}}'


  rmdirs:
    internal: true
    desc: 删除所有空目录
    cmds:
      - '{{.RCLONE}} rmdirs {{.RCLONE_REMOTE}}:{{.BUCKET}}/{{.BASE_PATH}} -v'
