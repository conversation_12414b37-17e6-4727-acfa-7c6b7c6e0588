---
version: '3'

vars:
  SOPS_FILE: secrets.yaml

tasks:
  # 编辑 secrets 文件
  edit:
    desc: "编辑加密的 secrets 文件"
    cmd: 'sops {{.SOPS_FILE}}'

  # 查看 secrets 文件内容
  view:
    desc: "查看解密后的 secrets 文件内容"
    cmd: 'sops -d {{.SOPS_FILE}}'

  # 加密 secrets 文件
  encrypt:
    desc: "加密 secrets 文件"
    cmd: 'sops -e {{.SOPS_FILE}}'

  # 解密 secrets 文件
  decrypt:
    desc: "解密 secrets 文件到临时文件"
    cmds:
      - 'sops -d {{.SOPS_FILE}} > secrets_decrypted.yaml'
      - 'echo "解密文件已保存为: secrets_decrypted.yaml"'

  # 验证 secrets 文件
  verify:
    desc: "验证 secrets 文件格式和内容"
    cmds:
      - 'echo "验证 secrets 文件格式..."'
      - 'sops -d {{.SOPS_FILE}} | yq eval . -'
      - 'echo "✅ secrets 文件格式正确"'

  # 更新 .sops.yaml 中的 age 公钥
  update-key:
    desc: "更新 .sops.yaml 中的 age 公钥"
    cmds:
      - |
        NEW_KEY=$(age-keygen -y {{.AGE_KEY}})
        echo "新的 age 公钥: $NEW_KEY"
        echo "请手动更新 .sops.yaml 文件中的 age_key 字段"

  # 重新加密所有 secrets
  reencrypt:
    desc: "重新加密所有 secrets"
    cmds:
      - 'sops updatekeys {{.SOPS_FILE}}'

  # 列出所有加密的键
  list-keys:
    desc: "列出所有加密的键"
    cmds:
      - 'sops -d {{.SOPS_FILE}} | yq eval "keys" -'

  # 获取特定键的值
  get:
    desc: "获取特定键的值"
    cmds:
      - |
        if [ -z "{{.CLI_ARGS}}" ]; then
          echo "用法: task get <键路径>"
          echo "示例: task get rclone.r2.access_key_id"
          echo "示例: task get ssh.github.private_key"
          exit 1
        fi
        KEY_PATH="{{.CLI_ARGS}}"
        sops -d {{.SOPS_FILE}} | yq eval ".$KEY_PATH" -

  # 设置特定键的值
  set:
    desc: "设置特定键的值"
    cmds:
      - |
        if [ -z "{{.CLI_ARGS}}" ]; then
          echo "用法: task set <键路径> <值>"
          echo "示例: task set rclone.r2.access_key_id 'new_key'"
          exit 1
        fi
        KEY_PATH="{{.CLI_ARGS}}"
        VALUE="${KEY_PATH#* }"
        KEY_PATH="${KEY_PATH%% *}"
        sops -d {{.SOPS_FILE}} | yq eval ".$KEY_PATH = \"$VALUE\"" - | sops -e /dev/stdin > {{.SOPS_FILE}}.tmp
        mv {{.SOPS_FILE}}.tmp {{.SOPS_FILE}}
        echo "已更新键: $KEY_PATH"

  # 清理临时文件
  clean:
    desc: "清理临时文件"
    cmds:
      - 'rm -f secrets_decrypted.yaml {{.SOPS_FILE}}.tmp'
      - 'echo "清理完成"'

  # 帮助信息
  help:
    desc: "显示帮助信息"
    cmds:
      - |
        echo "Sops 加密管理工具"
        echo "=================="
        echo ""
        echo "可用命令:"
        echo "  task keygen                    - 获取 age 公钥"
        echo "  task edit                      - 编辑加密的 secrets 文件"
        echo "  task view                      - 查看解密后的 secrets 文件内容"
        echo "  task encrypt                   - 加密 secrets 文件"
        echo "  task decrypt                   - 解密 secrets 文件到临时文件"
        echo "  task verify                    - 验证 secrets 文件格式和内容"
        echo "  task update-key                - 更新 .sops.yaml 中的 age 公钥"
        echo "  task reencrypt                 - 重新加密所有 secrets"
        echo "  task list-keys                 - 列出所有加密的键"
        echo "  task get <键路径>              - 获取特定键的值"
        echo "  task set <键路径> <值>         - 设置特定键的值"
        echo "  task clean                     - 清理临时文件"
        echo "  task help                      - 显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  task edit"
        echo "  task get rclone.r2.access_key_id"
        echo "  task set rclone.r2.access_key_id 'new_key'"
        echo "  task verify"
