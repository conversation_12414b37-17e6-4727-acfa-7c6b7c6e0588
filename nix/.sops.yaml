# Sops configuration file
# This file defines how sops encrypts and decrypts secrets

# Default key group for encryption
keys:
  # Add your age key here
  - &age_key age10prwj4t8u0cjjv3mhc2kcsgglkyhm4cht0yp9sgmkurwuctgddcq46n97r

# Default creation rules
creation_rules:
  # Rule for secrets in the secrets directory
  - path_regex: secrets/.*\.yaml$
    key_groups:
      - age:
          - *age_key
    # Don't encrypt the entire file, only specific keys
    unencrypted_suffix: _unencrypted
