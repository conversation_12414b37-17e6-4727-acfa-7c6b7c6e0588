# Darwin services configuration
# System services, daemons, and launchd configurations
{pkgs, ...}: {
  # Services configuration
  services = {
    # Enable nix daemon
    nix-daemon.enable = true;

    # Enable SSH agent
    ssh-agent.enable = true;

    # Enable locate database
    locate = {
      enable = true;
      interval = "daily";
      output = "/var/db/locate/database";
    };
  };

  # Auto upgrade nix-darwin
  system.autoUpgrade.enable = false; # Disabled by default, enable if needed
  system.autoUpgrade.dates = "04:00";
  system.autoUpgrade.allowReboot = false;
  system.autoUpgrade.flags = [
    "--update-input"
    "nixpkgs"
    "--update-input"
    "home-manager"
    "--commit-lock-file"
  ];

  # Service-related launchd configurations
  launchd.daemons = {
    # Service monitoring daemon
    "service-monitor" = {
      serviceConfig = {
        Label = "com.user.service.monitor";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # Service monitoring daemon
            while true; do
              # Monitor critical services
              for service in sshd nix-daemon locate; do
                if ! launchctl list | grep -q "$service"; then
                  echo "$(date): Service $service is not running" >> /var/log/service-monitor.log
                  # Attempt to restart the service
                  launchctl load /System/Library/LaunchDaemons/com.apple.$service.plist 2>/dev/null || true
                fi
              done

              # Monitor system resources
              CPU_USAGE=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | tr -d '%')
              MEMORY_USAGE=$(vm_stat | grep "Pages free" | awk '{print $3}')
              DISK_USAGE=$(df -h / | tail -1 | awk '{print $5}' | tr -d '%')

              echo "$(date): CPU: $CPU_USAGE%, Memory: $MEMORY_USAGE, Disk: $DISK_USAGE%" >> /var/log/service-monitor.log

              sleep 300  # Check every 5 minutes
            done
          ''
        ];
        RunAtLoad = true;
        KeepAlive = true;
        StandardOutPath = "/var/log/service-monitor.log";
        StandardErrorPath = "/var/log/service-monitor.log";
      };
    };

    # Log rotation service
    "log-rotate" = {
      serviceConfig = {
        Label = "com.user.log.rotate";
        ProgramArguments = [
          "${pkgs.logrotate}/bin/logrotate"
          "-f"
          "/etc/logrotate.conf"
        ];
        StartCalendarInterval = [
          {
            Hour = 0;
            Minute = 0;
          }
        ];
        StandardOutPath = "/var/log/log-rotate.log";
        StandardErrorPath = "/var/log/log-rotate.log";
      };
    };

    # System cleanup service
    "system-cleanup" = {
      serviceConfig = {
        Label = "com.user.system.cleanup";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # System cleanup script
            echo "$(date): Starting system cleanup" >> /var/log/system-cleanup.log

            # Clean up temporary files
            find /tmp -type f -mtime +7 -delete >> /var/log/system-cleanup.log 2>&1

            # Clean up old logs
            find /var/log -name "*.log" -mtime +30 -delete >> /var/log/system-cleanup.log 2>&1

            # Clean up cache files
            find /var/cache -type f -mtime +30 -delete >> /var/log/system-cleanup.log 2>&1

            # Clean up browser cache
            find ~/Library/Caches -type f -mtime +30 -delete >> /var/log/system-cleanup.log 2>&1

            echo "$(date): System cleanup completed" >> /var/log/system-cleanup.log
          ''
        ];
        StartCalendarInterval = [
          {
            Hour = 3;
            Minute = 0;
          }
        ];
        StandardOutPath = "/var/log/system-cleanup.log";
        StandardErrorPath = "/var/log/system-cleanup.log";
      };
    };

    # Performance monitoring service
    "performance-monitor" = {
      serviceConfig = {
        Label = "com.user.performance.monitor";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # Performance monitoring script
            while true; do
              # Collect performance metrics
              TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
              CPU_USAGE=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | tr -d '%')
              MEMORY_USAGE=$(vm_stat | grep "Pages free" | awk '{print $3}')
              DISK_USAGE=$(df -h / | tail -1 | awk '{print $5}' | tr -d '%')
              NETWORK_TRAFFIC=$(netstat -i | grep -E "en0|en1" | awk '{print $5, $7}')
              DISK_IO=$(iostat -d 1 1 | tail -1)

              # Log performance metrics
              echo "$TIMESTAMP - CPU: $CPU_USAGE%, Memory: $MEMORY_USAGE, Disk: $DISK_USAGE%, Network: $NETWORK_TRAFFICK, IO: $DISK_IO" >> /var/log/performance-monitor.log

              sleep 60  # Collect metrics every minute
            done
          ''
        ];
        RunAtLoad = true;
        KeepAlive = true;
        StandardOutPath = "/var/log/performance-monitor.log";
        StandardErrorPath = "/var/log/performance-monitor.log";
      };
    };
  };
}
