---
version: '3'

env:
  SSH_KEY_TYPE: ed25519
  SSH_KEY_DIR: ~/.ssh


tasks:
  default:
    desc: 用sshs列出本地所有ssh，并直接连接
    interactive: true
    cmd: sshs

  setup-ssh:
    desc: Setup SSH for a new host
    cmds:
      - task: ssh:generate-key
      - task: ssh:copy-key
      - task: ssh:set-permissions

  # 默认使用 rsa 算法生成key，但是建议使用 ed25519算法，更安全更快。使用 -C 来标识，比如说github就标识gh，我通常直接把 identifier 和 passphrase密码 设置为相同的，防止忘掉。产生公钥与私钥对，其中id_rsa 私钥，保留不动即可，后续 ssh 命令会自动读取此文件。id_rsa.pub 公钥，此文件需要被保存至目标服务器，用作验证。
  generate-key:
    desc: 生成 SSH 密钥对（task -g ssh:generate-key HOST_ALIAS=github）
    cmds:
      - ssh-keygen -t {{.SSH_KEY_TYPE}} -C "{{.HOST_ALIAS}}" -f {{.SSH_KEY_DIR}}/id_{{.HOST_ALIAS}}
  # ssh-keygen -t ed25519 -f my_github_ed25519  -C "me@github"
  # ssh-keygen -t ed25519 -f my_gitee_ed25519   -C "me@gitee" # 我在 Gitee
  # ssh-keygen -t ed25519 -f my_gitlab_ed25519  -C "me@gitlab" # 我在 GitLab
  # ssh-keygen -t ed25519 -f my_company_ed25519 -C "<EMAIL>" # 我在企业
  # 产生公钥与私钥对
  # id_rsa 私钥，保留不动即可，后续 ssh 命令会自动读取此文件。
  # id_rsa.pub 公钥，此文件需要被保存至目标服务器，用作验证。



  # 上传公钥到目标服务器（将本机的公钥复制到远程机器的authorized_keys文件中）
  # 相当于 pbcopy命令。
  # ⚠️ 复制之后最好在服务端验证一下。
  copy-key:
    desc: 上传公钥到目标服务器（task -g ssh:copy-key ）
    cmds:
      - ssh-copy-id -i {{.SSH_KEY_DIR}}/id_{{.HOST_ALIAS}}.pub {{.USER}}@{{.HOST}}
  # <AUTHOR> <EMAIL>
  #  # 指定 pub
  #  ssh-copy-id -i <~/.ssh/id_rsa.pub> <user>@<ip>



  # 在 客户端 设置权限
  # 修改 known_hosts文件 的权限
  # 修改 私钥和公钥 的权限
  set-permissions:
    desc: Set SSH file permissions
    cmds:
      - chmod 755 {{.SSH_KEY_DIR}}
      - chmod 600 {{.SSH_KEY_DIR}}/id_{{.HOST_ALIAS}}
      - chmod 600 {{.SSH_KEY_DIR}}/id_{{.HOST_ALIAS}}.pub
      - chmod 644 {{.SSH_KEY_DIR}}/known_hosts


  verify:
    desc: "task -g ssh:verify PPK_PATH=/etc/ssh/github/private_key"
    cmd: ssh-keygen -y -e -f {{.PPK_PATH}} >/dev/null && echo "格式合法" || echo "仍不合法"
