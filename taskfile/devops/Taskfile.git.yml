---
version: '3'

dir: '{{.USER_WORKING_DIR}}'



#- git reflog # reflog 是一个本地结构，它记录了 HEAD 和分支引用在过去指向的位置。reflog 信息没法与其他任何人共享，每个人都是自己特有的 reflog。重要的一点是，它不是永久保存的，有一个可配置的过期时间，reflog 中过期的信息会被自动删除。我们在使用reset后，部分代码会丢失，如果这时想找回这些代码，就可以使用reflog

#- git rm -r --cached . # 忽略规则不生效，清空git缓存，撤销已经写到缓存区文件的修改
#- git checkout . # 放弃本地所有修改


#- git reset [--mixed | --soft | --hard] <起始点的父提交> # 三种reset type，默认mixed 回退到某个版本，本地会保留源码，回退 commit 和 index 信息，若要提交重新 commit。soft 回退到某个版本，只回退了 commit 的信息，不会恢复到 index file 一级，若要提交重新 commit。Hard 彻底回退到某个版本，本地的源码也会变为上一个版本的内容。
#- git reset HEAD~3
#- git reset --merge # 用来撤销合并，也可以与mixed/soft/hard搭配使用
#- git rebase -i HEAD~n # 合并前n个commit。需要注意的是，编辑时把pick改为s即可，记得第一个不要修改（需要父commit）


vars:
  GH_TOKEN:
    sh: gh auth token
  COMMIT_HASH:
    sh: |
      if [ -d .git ] || git rev-parse --git-dir > /dev/null 2>&1; then
        git rev-parse HEAD
      else
        echo ""
      fi
    msg: commit hash not found



tasks:
  default:
  # Commented out to prevent auto-execution when included
  # desc: Init Git Repo
  # cmds:
  #   - REPO="{{.CLI_ARGS}}"
  #   - mkdir -p "$REPO" && cd "$REPO"
  #   - echo "# $REPO" >> README.md
  #   - git init
  #   - git add .
  #   - git commit -m "first commit"
  #   - git branch -M main
  #   - 'if ! git remote | grep -q origin; then git remote add origin "https://github.com/xbpk3t/$REPO.git"; fi'
  #   - git push -u origin main
  # silent: false


  config:
    desc:  # 其实就是.gitconfig
    cmd: git config --list
    interactive: true


  # 主报告任务
  analyze:
    desc: 在控制台显示完整的Git仓库分析报告
    preconditions:
      - 'test -d .git || { echo "错误: 当前目录不是Git仓库"; exit 1; }'
    cmds:
      - echo "================ 仓库概览 ================"
      - git-quick-stats --detailed-git-stats
      - echo "================ 贡献者排名 ================"
      - git-quick-stats --contributors
      - echo "================ 代码所有权分析 ================"
      - git who table -l -n 10 | head -n 15
      - echo "================ 目录责任分布 ================"
      - git who tree -d 2 | head -n 30
      - echo "================ 历史贡献趋势 ================"
      - git who hist -n 5 | head -n 20
      - echo "✅ Git仓库分析报告完成！"
    dir: '{{.USER_WORKING_DIR}}'

  # 查看 tag list
  tag-list:
    cmd:


  #  - url: https://github.com/Bhupesh-V/ugit
  #    des: 千呼万唤才出来的git工具，用来撤销git操作。最经典的场景就是，经常有那种已经commit了，然后还有点代码想放到那个commit里提交上去。这个就很难操作，这种情况下用ugit就很容易了。
  # 使用 ugit 撤销上一次 Git 操作（支持交互式选择）
  # 怎么修改（上一次commit的，以及指定commit的） commit message? # git rebase -i：修改某次 commit 的 message。
  undo:
    desc: "使用 ugit 撤销上一次 Git 操作（支持交互式菜单）"
    silent: true
    cmd: |
      if [ -z "{{.CLI_ARGS}}"]; then
      ugit;
      else
      ugit undo {{.CLI_ARGS}};
      fi
    dir: '{{.USER_WORKING_DIR}}'

  #- 操作场景: 【回滚本地未提交代码】丢弃未暂存更改
  #  原生 Git 命令: "git restore . 或 git checkout -- ."
  #  ugit 替代方案: "ugit → 选择 Undo git add"
  #
  #- 操作场景: 【回滚本地已提交的代码】
  #  原生 Git 命令: "git reset --soft HEAD~1（保留修改）；git reset --hard HEAD~1（彻底删除）"
  #  ugit 替代方案: "ugit → 选择 Undo git commit"
  #
  #- 操作场景: 回滚所有未暂存/未贮藏代码
  #  原生 Git 命令: "git restore . && git clean -df（含未跟踪文件）"
  #  ugit 替代方案: "ugit → 组合使用 Undo git add + git clean"
  #
  #- 操作场景: 【撤回已 push 的提交】
  #  原生 Git 命令: "git revert <commit>（安全）；git reset --hard <commit> && git push -f（危险）"
  #  ugit 替代方案: "ugit → 选择 Undo git push"
  #
  #- 操作场景: 恢复误删除的 commit
  #  原生 Git 命令: "git reflog → 找到 commit hash → git reset --hard <hash>"
  #  ugit 替代方案: "ugit → 选择 Undo git reset"
  #
  #- 操作场景: 彻底删除历史 commit
  #  原生 Git 命令: "git reset --hard <commit-id>（本地）；git push origin HEAD --force（远程）"
  #  ugit 替代方案: "ugit 仅支持删除最近 commit（等效 reset HEAD~1）"



  # https://github.com/newren/git-filter-repo
  rewrite:
    desc: 怎么压缩 git 项目？
    cmds:
      # - git rev-list --objects --all | grep "$(git verify-pack -v .git/objects/pack/*.idx | sort -k 3 -n | tail -10 | awk '{print$1}')"

      # --path: 指定要操作的路径
      # --invert-paths: 反转选择，即“删除这些路径，保留其他所有”
      # --force: 强制运行（因为filter-repo默认在非全新克隆的仓库上运行会提示风险）
      - git filter-repo --path {{.FILES}} --invert-paths --force

      # 推送到远程（因为历史已经改变，必须强制推送）
      - git push origin --force --all
    requires:
      vars: [FILES]


  #- url: https://github.com/kubernetes/git-sync
  #  des: 怎么保证repo和其upstream同步?
  # fork 后如何同步源的最新代码：我fork出来的分支，怎么同步后来父分支的更新？ # [Syncing a fork - GitHub Docs](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/syncing-a-fork) git remote add <upstream> && git fetch <upstream> && git checkout main && git merge <upstream>/master
  sync-upstream:


  # 怎么合并多个 commit? rebase、merge、cherry pick都可以合并多个 commit，有啥区别? Why we'd better use merge?***" # rebase 操作和 cherry-pick 操作都会修改 commit-id，导致无法追溯问题。所以通常禁止使用，只允许 merge 操作公共分支（merge将两个分支上的所有提交记录合并成一个新的提交记录，并且保留原来的提交记录。这个操作不会修改提交记录的 SHA 值），方便 debug。
  merge-commit:


  #- url: https://github.com/gruntwork-io/fetch
  #  des: 非常好用的工具，用来直接拉取github的指定folder（注意只能用于github）。工具很好，但是不适合日常使用，3个required参数，用起来有点麻烦。
  fetch:
    desc: 下载指定文件夹（使用 gruntwork-io/fetch）
    preconditions:
      - sh: command -v fetch
      - sh: command -v gh
    vars:
      DEFAULT_BRANCH:
        sh: gh repo view {{.CLI_ARGS}} --json defaultBranchRef --jq .defaultBranchRef.name
      REPO_NAME: '{{.CLI_ARGS | splitList "/" | last}}'
    cmd: fetch --repo="{{.CLI_ARGS}}" --github-oauth-token={{.GH_TOKEN}} --branch="{{.DEFAULT_BRANCH}}" {{.REPO_NAME}}
    dir: '{{.USER_WORKING_DIR}}'
    interactive: true


  #  - git gc 是啥意思？有啥用？ # [git clean - Reduce Git repository size - Stack Overflow](https://stackoverflow.com/questions/2116778/reduce-git-repository-size)
  #  - If I delete a git branch, whether .git folder size will decrease or not? git gc # git gc --prune=now
  git-gc:


  token:
    silent: true
    interactive: true
    cmd: 'echo {{.GH_TOKEN}}'


  #- url: https://github.com/cli/cli
  # [Manual | GitHub CLI](https://cli.github.com/manual/)
  gist:clear:
    desc: 清空所有gist
    interactive: true
    cmds:
      - "echo gist numbers: {{.GIST_LIST | splitLines | len}}"
      - for:
          var: GIST_LIST
          split: "\n"
        cmd: "gh gist delete --yes {{.ITEM}}"
    vars:
      GIST_LIST:
        sh: gh gist list --limit 100 | awk '{print $1}'

  # 我现在main分支的代码挂了，我怎么把代码切到之前的某个正常commit，并且把这部分新增的commit切到一个新branch？操作的具体流程 # git checkout -b <new-branch> <existed-branch> && git reset --hard <commit-id> && git push -f

  # 怎么判断某个热门开源项目是不是草台班子，有啥技巧 # [那些年，我被坑过的开源项目【让编程再次伟大#19】_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1u7mjYpETD/) 11:20 这段，牛逼。按 comments desc查看issue，在吵的

  # git config --global pull.rebase true # 设置 pull 时自动 rebase（避免意外合并提交）


  size:
    desc: 显示 Git 对象大小信息
    interactive: true
    cmds:
      - git count-objects -v
      - git remote -v # 查看git的远程仓库地址
    dir: '{{.USER_WORKING_DIR}}'


  size-top:
    desc: 查看repo里的TopK大小的文件
    cmd: git verify-pack -v .git/objects/pack/*.idx | sort -k 3 -n | tail -{{.size}} | awk '{print$1}' | xargs -I {} sh -c 'git rev-list --objects --all | grep {}' || true
    interactive: true
    dir: '{{.USER_WORKING_DIR}}'
    vars:
      size: "{{.size | default 20}}"

  #- git log --graph --date=short # 查看提交记录，相当于 glods
  #- glods --grep <keywords> # 搜索提交记录，非常好用
  glods:




  #- git grep -n -p <str> # 从当前branch中查找指定字符串，相比于 'git grep -n <str>' 会显示匹配行以及上下几行的内容
  #- git grep -n -p <str> <commit号/tag号> # 从指定commit中查找指定字符串
  #- git rev-list --all | xargs git grep <str> # 从所有历史commit中查找指定字符串
  search:
    desc: 从所有历史commit中查找指定字符串
    cmd: git rev-list --all | xargs git grep {{.str}}
    vars:
      str: '{{.str}}'
    interactive: true
    silent: true
    dir: '{{.USER_WORKING_DIR}}'


  #- git remote rm origin && git remote add origin <url> # git 怎么修改远程仓库地址
  modify-origin:
    desc: 修改远程仓库地址
    cmd: git remote rm origin && git remote add origin {{.CLI_ARGS}}
    dir: '{{.USER_WORKING_DIR}}'
