---
version: '3'

includes:
  git: taskfile/devops/Taskfile.git.yml
  linters: taskfile/devops/Taskfile.linters.yml
  ansible: taskfile/devops/Taskfile.ansible.yml
  gor: taskfile/devops/Taskfile.goreleaser.yml
  caddy: taskfile/devops/Taskfile.caddy.yml
  pc: taskfile/devops/Taskfile.pre-commit.yml
  wrangler: taskfile/devops/Taskfile.wrangler.yml
  rclone: taskfile/devops/Taskfile.rclone.yml
  tf: taskfile/devops/Taskfile.tf.yml

  gz: taskfile/go/Taskfile.gz.yml
  go: taskfile/go/Taskfile.go.yml


  k3d: taskfile/k8s/Taskfile.k3d.yml
  docker: taskfile/k8s/Taskfile.docker.yml
  mk: taskfile/k8s/Taskfile.minikube.yml
  dc: taskfile/k8s/Taskfile.dc.yml
  helm: taskfile/k8s/Taskfile.helm.yml
  k8s: taskfile/k8s/Taskfile.k8s.yml


  linux: taskfile/kernel/Taskfile.linux.yml
  monitor: taskfile/kernel/Taskfile.monitor.yml
  pkg: taskfile/kernel/Taskfile.pkg.yml
  net: taskfile/kernel/Taskfile.network.yml
  shell: taskfile/kernel/Taskfile.shell.yml
  ssh: taskfile/kernel/Taskfile.ssh.yml
  tcpdump: taskfile/kernel/Taskfile.tcpdump.yml


  mac: taskfile/mac/Taskfile.mac.yml
  mac-cleanup: taskfile/mac/Taskfile.mac-cleanup.yml
  brew: taskfile/mac/Taskfile.brew.yml
  img: taskfile/mac/Taskfile.img.yml
  trzsz: taskfile/mac/Taskfile.trzsz.yml
  scrapy: taskfile/mac/Taskfile.scrapy.yml
  me: taskfile/mac/Taskfile.me.yml
  alfred: taskfile/mac/Taskfile.alfred.yml
  pnpm: taskfile/mac/Taskfile.pnpm.yml

  sec: taskfile/works/Taskfile.sec.yml


tasks:
  default:
    silent: true
    interactive: true
    cmd: task -l
