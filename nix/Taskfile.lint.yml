version: '3'

vars:
  NIX_DIR: '.'

tasks:
  default:
    desc: "运行所有 Nix linting 工具"
    cmds:
      - echo "🔍 运行 Nix linting 工具..."
      - task: lint
      - echo "✅ Nix linting 完成"

  lint:
    desc: "运行所有 Nix linting 工具"
    dir: '{{.NIX_DIR}}'
    cmds:
      - task: deadnix
      - task: statix

  deadnix:
    desc: "使用 deadnix 检测未使用的 Nix 代码"
    dir: '{{.NIX_DIR}}'
    cmds:
      - echo "🔍 运行 deadnix 检查未使用的 Nix 代码..."
      - deadnix .
      - echo "✅ deadnix 检查完成"

  statix:
    desc: "使用 statix 分析和修复 Nix 代码"
    dir: '{{.NIX_DIR}}'
    cmds:
      - echo "🔍 运行 statix 分析 Nix 代码..."
      - statix check .
      - echo "✅ statix 检查完成"

  fix:
    desc: "自动修复可修复的 Nix 代码问题"
    dir: '{{.NIX_DIR}}'
    cmds:
      - echo "🔧 运行 statix 自动修复..."
      - statix fix .
      - echo "✅ 自动修复完成"
