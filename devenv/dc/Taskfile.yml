---
version: '3'

env:
  dir: '{{.USER_WORKING_DIR}}'

vars:
  COMPOSE_FILE: "./devenv/docker-compose.yml"
  COMPOSE_CMD: '{{.COMPOSE_CMD | default "docker-compose"}}'
  ENV_FILE: '{{.ENV_FILE | default "./devenv/project1.env"}}'
  COMPOSE_FULL_CMD: '{{.COMPOSE_CMD | default "docker-compose"}} -f {{.COMPOSE_FILE}} --env-file {{.ENV_FILE}}'
  ALL_PROFILES:
    - dev
    - db
    - pag
    - trace
    - ms
    - ck
    - n9e
    - n8n

tasks:
  up:
    desc: Build images and start services with selected profile
    interactive: true
    cmds:
      - task: docker-compose-action
        vars:
          ACTION: up -d --build
    preconditions:
      - sh: '[ -n "{{.CLI_ARGS}}" ] || [ -n "{{.SELECTED_PROFILE}}" ]'
        msg: "未选择 profile"

  logs:
    desc: View service logs with selected profile
    interactive: true
    cmds:
      - task: docker-compose-action
        vars:
          ACTION: logs -f --tail {{.LINES | default 50}} {{.SERVICE}}

  exec:
    desc: Enter container terminal with selected service
    interactive: true
    vars:
      SELECTED_SERVICE:
        sh: SERVICES=$({{.COMPOSE_FULL_CMD}} ps --services 2>/dev/null); if [ -n "$SERVICES" ]; then echo "选择要进入的 service:" >&2; echo "$SERVICES" | gum choose || echo ""; else echo "" > /dev/stderr; echo ""; fi
    cmds:
      - |
        {{if .CLI_ARGS}}
          {{.COMPOSE_FULL_CMD}} exec {{.CLI_ARGS}} {{.SHELL | default "/bin/bash"}}
        {{else}}
          {{.COMPOSE_FULL_CMD}} exec {{.SELECTED_SERVICE}} {{.SHELL | default "/bin/bash"}}
        {{end}}

  docker-compose-action:
    interactive: true
    silent: true
    cmd: |
      {{if .CLI_ARGS}}
        {{.COMPOSE_FULL_CMD}} --profile {{.CLI_ARGS}} {{.ACTION}}
      {{else}}
        echo "选择要操作的 profile:" >&2
        SELECTED_PROFILE=$(echo "{{join "\n" .ALL_PROFILES}}" | gum choose || echo "")
        if [ -n "$SELECTED_PROFILE" ]; then
          {{.COMPOSE_FULL_CMD}} --profile $SELECTED_PROFILE {{.ACTION}}
        else
          echo "未选择 profile" >&2
          exit 1
        fi
      {{end}}


  ps:
    desc: View running container status
    cmds:
      - "{{.COMPOSE_FULL_CMD}} ps"

  config:
    desc: Validate and view compose configuration
    cmds:
      - "{{.COMPOSE_FULL_CMD}} config"


  init:
    desc: Initialize development environment (create .env file)
    cmds:
      - cp ../../devenv/.env.example {{.ENV_FILE}}
      - echo "Please edit {{.ENV_FILE}} to configure your environment variables"

  init-project:
    desc: Initialize project-specific environment configuration
    cmds:
      - cp ../../devenv/.env.example {{.ENV_FILE}}
      - echo "Created project environment config file {{.ENV_FILE}}"

  clean:
    desc: Clean all containers, networks and volumes
    cmds:
      - "{{.COMPOSE_FULL_CMD}} down -v --remove-orphans"
      - "docker system prune -f"

  health:
    desc: Check all service health status
    cmds:
      - "{{.COMPOSE_FULL_CMD}} ps --format table"
