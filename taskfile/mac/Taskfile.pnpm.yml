---
version: "3"


#- pnpm config set prefix <目标目录> # 修改全局包位置
#- pnpm install -g <pkg> # 全局安装
#- pnpm rm -g <pkg> # 全局卸载，相当于npm uninstall，rm是uninstall的alias，需要注意的是只写pkg名就可以了，不要加version. 比如 npm rm -g create-react-app.

tasks:
  config:
    cmds:
      - echo "所有全局包列表:"
      - pnpm list -g
      - echo "全局包位置:"
      - pnpm root -g
      - echo "【存储管理】store位置:"
      - pnpm store path

  optimize:
    cmds:
      - pnpm store prune # 移除所有全局unused pkg


  #- url: https://github.com/depcheck/depcheck
  #  des: depcheck = npm-check, 但是只能查找unused dep
  # [webpro-nl/knip: ✂️ Find unused files, dependencies and exports in your JavaScript and TypeScript projects. Knip it before you ship it!](https://github.com/webpro-nl/knip)
  depcheck:
    desc: 更新
    cmds:
      - pnpm outdated
      - pnpm update --latest
      - knip --fix
      - task: install # post-fix需要重新install来uninstall这些unused依赖
      - npm-check -u
      - npm-check
    dir: '{{.USER_WORKING_DIR}}'
    interactive: true
