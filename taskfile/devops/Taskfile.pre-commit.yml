version: "3"

# init-templatedir    Install hook script in a directory intended for use with `git config init.templateDir`.
# install             Install the pre-commit script.
# install-hooks       Install hook environments for all environments in the config file. You may find `pre-commit install --install-hooks` more useful.
# migrate-config      Migrate list configuration to new map configuration.
# run                 Run hooks.
# sample-config       Produce a sample .pre-commit-config.yaml file
# try-repo            Try the hooks in a repository, useful for developing new hooks.
# uninstall           Uninstall the pre-commit script.
# validate-config     Validate .pre-commit-config.yaml files
# validate-manifest   Validate .pre-commit-hooks.yaml files
# help                Show help for a specific command.

vars:
  PC_CLI: pre-commit
  HOOKS:
    - yamllint
    - golangci-lint
    - golangci-lint-full
    - markdownlint
    - nilaway
    - betteralign
    - gitleaks


tasks:
  default:
    cmd: "{{.PC_CLI}} run --all-files"  # 检查所有文件（非暂存区）
    dir: '{{.USER_WORKING_DIR}}'
    interactive: true
    defer:
      - task: update


  update:
    desc: 更新远程仓库版本
    cmd: '{{.PC_CLI}} autoupdate'
    dir: '{{.USER_WORKING_DIR}}'
    internal: true


  # 新增维护任务
  maintain:
    desc: 对全局Pre-commit的hook进行 clean+gc+重装 操作
    cmds:
      - "{{.PC_CLI}} clean"  # 清理缓存
      - "{{.PC_CLI}} gc"     # 垃圾回收
      - "{{.PC_CLI}} install-hooks"  # 重装钩子
      - task: update
    dir: '{{.USER_WORKING_DIR}}'

  init-template:
    cmd: "{{.PC_CLI}} init-templatedir {{.CLI_ARGS}}"  # 初始化Git模板
    dir: '{{.USER_WORKING_DIR}}'

  uninstall:
    cmd: "{{.PC_CLI}} uninstall"  # 卸载钩子
    dir: '{{.USER_WORKING_DIR}}'

  migrate:
    cmd: '{{.PC_CLI}} migrate-config'  # 配置文件迁移
    dir: '{{.USER_WORKING_DIR}}'

  try-repo:
    cmd: '{{.PC_CLI}} try-repo {{.CLI_ARGS}}'  # 测试新仓库
    dir: '{{.USER_WORKING_DIR}}'

  hook:
    desc: 执行指定hook（交互式选择或直接运行检查）
    interactive: true
    dir: '{{.USER_WORKING_DIR}}'
    cmd: '{{.PC_CLI}} run {{.SELECTED}}'
    vars:
      SELECTED:
        sh: gum choose {{.HOOKS | join " "}}
