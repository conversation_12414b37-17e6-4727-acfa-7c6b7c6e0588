---
version: '3'

# [Arch、Debian 包管理工具常用命令速查表 | <PERSON><PERSON>'s Blog](https://blog.yuanji.dev/posts/arch-debian-package-manager-commands-cheatsheet/)

vars:
  # 检测当前发行版
  DISTRO:
    sh: |
      if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo $ID
      elif command -v lsb_release >/dev/null 2>&1; then
        lsb_release -i | cut -f2
      else
        uname -s
      fi

  # 定义各发行版的命令映射
  CMD_MAP:
    map:
      arch_info: "包管理器: pacman"
      debian_info: "包管理器: apt/dpkg"
      ubuntu_info: "包管理器: apt/dpkg"
      fedora_info: "包管理器: dnf"
      nixos_info: "包管理器: nix"
      unknown_info: "未知发行版，可能支持的包管理器: pacman, apt, dnf, nix"

      arch_sync: "sudo pacman -Sy"
      debian_sync: "sudo apt update"
      ubuntu_sync: "sudo apt update"
      fedora_sync: "sudo dnf check-update"
      nixos_sync: "sudo nix-channel --update"
      unknown_sync: "echo '不支持的发行版或未实现的包管理器'"

      arch_search: "pacman -Ss"
      debian_search: "apt search"
      ubuntu_search: "apt search"
      fedora_search: "dnf search"
      nixos_search: "nix search nixpkgs"
      unknown_search: "echo '不支持的发行版或未实现的包管理器'"

      arch_search_file: "pacman -F"
      debian_search_file: "apt-file search"
      ubuntu_search_file: "apt-file search"
      fedora_search_file: "dnf provides"
      nixos_search_file: "nix-locate"
      unknown_search_file: "echo '不支持的发行版或未实现的包管理器'"

      arch_show: "pacman -Si"
      debian_show: "apt show"
      ubuntu_show: "apt show"
      fedora_show: "dnf info"
      nixos_show: "nix search nixpkgs --verbose"
      unknown_show: "echo '不支持的发行版或未实现的包管理器'"

      arch_upgrade: "sudo pacman -Syu"
      debian_upgrade: "sudo apt update && sudo apt upgrade"
      ubuntu_upgrade: "sudo apt update && sudo apt upgrade"
      fedora_upgrade: "sudo dnf upgrade"
      nixos_upgrade: "sudo nixos-rebuild switch --upgrade"
      unknown_upgrade: "echo '不支持的发行版或未实现的包管理器'"

      arch_install: "sudo pacman -S"
      debian_install: "sudo apt install"
      ubuntu_install: "sudo apt install"
      fedora_install: "sudo dnf install"
      nixos_install: "nix-env -i"
      unknown_install: "echo '不支持的发行版或未实现的包管理器'"

      arch_list_installed: "pacman -Q"
      debian_list_installed: "apt list --installed"
      ubuntu_list_installed: "apt list --installed"
      fedora_list_installed: "dnf list installed"
      nixos_list_installed: "nix-env -q"
      unknown_list_installed: "echo '不支持的发行版或未实现的包管理器'"

      arch_list_package: "pacman -Qi"
      debian_list_package: "dpkg -s"
      ubuntu_list_package: "dpkg -s"
      fedora_list_package: "dnf list installed"
      nixos_list_package: "nix-env -q"
      unknown_list_package: "echo '不支持的发行版或未实现的包管理器'"

      arch_list_upgradable: "pacman -Qu"
      debian_list_upgradable: "apt list --upgradable"
      ubuntu_list_upgradable: "apt list --upgradable"
      fedora_list_upgradable: "dnf list upgrades"
      nixos_list_upgradable: "nix-env -q --outdated"
      unknown_list_upgradable: "echo '不支持的发行版或未实现的包管理器'"

      arch_remove: "sudo pacman -Rs"
      debian_remove: "sudo apt remove && sudo apt autoremove"
      ubuntu_remove: "sudo apt remove && sudo apt autoremove"
      fedora_remove: "sudo dnf remove"
      nixos_remove: "nix-env -e"
      unknown_remove: "echo '不支持的发行版或未实现的包管理器'"

tasks:

  run:*:
    silent: true
    vars:
      SERVICE: '{{index .MATCH 0}}'
    requires:
      vars:
        - name: SERVICE
          enum:
            - info # 显示当前系统和包管理器信息
            - sync # 同步软件包仓库
            - search # 搜索软件包
            - search_file # 搜索包含特定文件的软件包
            - show # 查看软件包信息
            - upgrade # 更新系统（安装所有可用更新）
            - install # 安装软件包
            - list_installed # 查看已安装软件包
            - list_package # 查看特定已安装软件包信息
            - list_upgradable # 查看可升级软件包
            - remove # 删除软件包及依赖
    cmds:
      - echo "Starting {{.SERVICE}}"
      - task: execute
        vars:
          ACTION: "{{.SERVICE}}"
          ARGS: "{{.CLI_ARGS}}"


  # 执行命令的模板任务
  execute:
    internal: true
    vars:
      ACTION: '{{.ACTION}}'
      ARGS: '{{.ARGS | default ""}}'
      DISTRO_KEY: '{{if eq .DISTRO "arch"}}arch{{else if eq .DISTRO "debian"}}debian{{else if eq .DISTRO "ubuntu"}}ubuntu{{else if eq .DISTRO "fedora"}}fedora{{else if eq .DISTRO "nixos"}}nixos{{else}}unknown{{end}}'
      CMD_KEY: '{{printf "%s_%s" .DISTRO_KEY .ACTION}}'
      CMD: '{{index .CMD_MAP .CMD_KEY}}'
    cmds:
      - |
        {{if eq .ARGS ""}}{{.CMD}}
        {{else}}{{.CMD}} {{.ARGS}}
        {{end}}
