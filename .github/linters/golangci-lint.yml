---
# golangci-lint configuration
# https://golangci-lint.run/usage/configuration/
# 基于《100 Go Mistakes and How to Avoid Them》的完整配置

# 配置文件版本
version: 2


#output:
#  formats:
#    text: # colored-line-number
#      path: stdout
#      colors: true
#      print-issued-lines: true
#      print-linter-name: true


linters:
  exclusions:
    paths:
      - ".*_test\\.go"


  default: all
  disable:
    - depguard
    - testpackage     # 测试包检查 - 禁用过于严格的测试包命名要求
    - wrapcheck       # 错误包装检查 - 禁用过于严格的错误包装要求
    - wsl
    - wsl_v5
    - err113
    - exhaustive
    - exhaustruct
    - funcorder
    - godox
    - gosmopolitan
    - intrange
    - ireturn
    - mnd
    - noctx
    - noinlineerr
    - paralleltest
    - prealloc
    - recvcheck
    - unparam


  settings:
    gocritic:
      enabled-tags:
        - diagnostic
        - experimental
        - opinionated
        - performance
        - style
      disabled-checks:
        - dupImport
        - ifElseChain
        - octalLiteral
        - whyNoLint
        - wrapperFunc
        - exitAfterDefer   # 已经默认启用，移除重复配置

      # 特定规则配置
      settings:
        rangeExprCopy:
          sizeThreshold: 32  # #21(切片预分配) 超过32字节强制预分配
        truncateCmp:
          skipArchDependent: false
        hugeParam:
          sizeThreshold: 80  # #95(堆栈分配) 大参数阈值

    # ===== revive 配置 =====
    revive: # 针对《100个错误》的规则配置
      rules:
        - name: package-comments  # #15(文档缺失)
          severity: warning
          disabled: true

        - name: receiver-naming    # #42(接收器命名)
          severity: warning
          disabled: false
          exclude: [""]
          arguments:
            - max-length: 4

        - name: cognitive-complexity # #2(认知复杂度)
          severity: info
          arguments: [9]

        - name: var-naming         # #1(变量命名)
          severity: warning
          arguments:
            - ["ID", "HTTP", "API", "URL"] # Allowlist for correct initialisms
            - ["Id", "Http", "Url", "VM"] # Blocklist for incorrect initialisms
          message: "Use Go-style naming conventions (e.g., 'HTTP' for 'Http')"

        # 补充的最佳实践规则
        - name: function-length    # 函数长度限制 - 单一职责原则
          arguments: [50, 0]

        - name: max-public-structs # 最大公共结构体数量 - 避免过度暴露
          arguments: [10]

        - name: range              # range使用检查 - 优化range使用

        - name: struct-tag         # 结构体标签检查 - 规范标签使用
          severity: warning
          disabled: false
          exclude: [""]
          arguments:
            - "json,inline"
            - "bson,outline,gnu"

        - name: unnecessary-stmt   # 不必要的语句检查 - 简化代码
          arguments: [true]

        - name: unused-receiver    # 未使用的接收器检查 - 避免无用的方法
          disabled: true
          severity: warning
          exclude: [""]
          arguments:
            - allow-regex: "^_"

        - name: exported           # 导出检查 - 避免过度导出
          severity: warning
          disabled: false
          exclude: [""]
          arguments:
            - "check-private-receivers"
            - "disable-stuttering-check"
            - "say-repetitive-instead-of-stutters"
            - "check-public-interface"
            - "disable-checks-on-constants"
            - "disable-checks-on-functions"
            - "disable-checks-on-methods"
            - "disable-checks-on-types"
            - "disable-checks-on-variables"

    # ===== 其他linter配置 =====
    gocognit:
      min-complexity: 35  # #90(测试函数复杂度阈值) - 进一步放宽，允许数据处理函数

    govet:
      enable-all: true

    gocyclo:
      min-complexity: 15  # #2(循环复杂度)

    dupl:
      threshold: 100

    goconst:
      min-len: 2
      min-occurrences: 2

    misspell:
      locale: US

    lll:
      line-length: 140

    # ===== 性能相关配置 =====
    staticcheck:
      checks: ["all", "-ST1000", "-ST1003", "-ST1016", "-ST1020", "-ST1021", "-ST1022"]

    # ===== 补充的最佳实践配置 =====
    gochecknoglobals: # 允许的全局变量类型
      allow: ["errors", "sync.Once", "init"]

    funlen:
      lines: 120  # 进一步放宽函数行数限制，允许数据处理函数
      statements: 80  # 进一步放宽语句数限制

    forbidigo: # 禁止使用的标识符
      forbid:


    nestif:
      min-complexity: 4  # 嵌套if的复杂度阈值

    testpackage: # 测试包检查配置
      skip-imports: ["testing"]

    wrapcheck: # 错误包装检查配置
      ignoreSigs:
        - "fmt\\.Errorf"
        - "errors\\.Wrap"
        - "errors\\.Wrapf"

    errorlint: # 错误处理检查配置
      errorf: true
      asserts: true
      comparison: true

    # [securego/gosec: Golang security checker](https://github.com/securego/gosec)
    gosec:
      excludes:
        - G104 # 部分error不需要处理
        - G107 # http请求时不能使用动态url，关闭
        - G115 # 整数转换误报
        - G204 # 命令执行误报
        - G304 # 文件包含误报
        - G307 # Deferring a method which returns an error

    varnamelen:
      max-distance: 6
      min-name-length: 1
      check-receiver: true
      check-return: true
      check-type-param: true
      ignore-type-assert-ok: true
      ignore-map-index-ok: true
      ignore-chan-recv-ok: true
      ignore-names:
        - err
      ignore-decls:
        - c echo.Context
        - t testing.T
        - f *foo.Bar
        - e error
        - i int
        - const C
        - T any
        - m map[string]int


issues:
  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0
  new: false
  exclude-files:
    - ".*\\.pb\\.go$"
    - ".*_test\\.go$"
  exclude-dirs:
    - vendor
    - node_modules
  exclude-rules:

    # 只在测试文件中忽略函数长度检查
    - path: _test\.go
      linters:
        - funlen
        - goconst
        - gocognit
        - revive

    - linters:
        - lll
      source: "^//go:generate "

    # 只在包含特定注释的代码行忽略常量检查
    - linters:
        - goconst
      source: "^// nolint:"

    # 排除大部分revive的过于严格的规则
    # 只在错误信息匹配特定模式时忽略
    - linters:
        - revive
      text: "(should have comment or be unexported|exported.*should have comment|package-comments|blank-imports|redefines-builtin-id|stutters|comment on exported.*should be of the form|var-naming)"


severity:
  default: error
  rules:
    - severity: info
      linters:
        - foo
      text: '(?i)Example.*'
