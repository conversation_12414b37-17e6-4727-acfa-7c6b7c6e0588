# Stylix 配置总结

## 已完成的配置

### 1. 添加 Stylix 到 Flake 配置
- 在 `nix/flake.nix` 中添加了 Stylix 输入
- 设置了正确的 `nixpkgs.follows` 以确保包版本一致性

### 2. 创建 Stylix 系统配置文件
- 创建了 `nix/modules/darwin/stylix.nix` 配置文件
- 配置了 Gruvbox Dark Hard 主题
- 设置了字体 (JetBrainsMono Nerd Font)
- 配置了光标样式 (Bibata-Modern-Ice)
- 添加了在线壁纸

### 3. 集成到 Darwin 系统模块
- 在 `nix/modules/darwin/default.nix` 中添加了 `./stylix.nix` 导入
- 配置了 Home Manager 自动继承系统主题
- Stylix 作为系统级模块管理整个环境的主题

### 4. 支持 Stylix 的应用配置
- **Neovim**: 移除了手动颜色设置，让 Stylix 管理 base16 主题
- **Yazi**: 移除了手动主题配置，让 Stylix 管理
- **Fzf**: 移除了手动颜色设置，让 Stylix 管理
- **Starship**: 创建了完整的配置文件，Stylix 会自动设置颜色

## 配置特点

### 🎨 统一的主题管理
- 所有支持的应用都使用相同的颜色方案
- 基于 base16 标准，确保颜色一致性
- 支持自动从壁纸生成颜色方案

### 🎯 自动化配置
- 无需手动配置每个应用的颜色
- 自动检测并主题化已安装的应用
- 支持 "it just works" 哲学

### 🔄 系统级管理
- Stylix 现在作为 Darwin 系统模块运行
- 自动为所有用户应用主题
- 更好的权限管理和系统集成

### 🚀 正确的架构分层
- 系统级配置：`nix/modules/darwin/stylix.nix`
- 用户级配置：自动继承系统主题
- 应用级配置：移除手动颜色设置，避免冲突

## 使用方法

### 应用配置
```bash
# 切换到 dotfiles 目录
cd /Users/<USER>/Desktop/dotfiles

# 构建并应用配置
darwin-rebuild switch --flake .#luck
```

### 自定义主题
编辑 `nix/modules/darwin/stylix.nix` 文件：

1. **更换颜色方案**：
   ```nix
   stylix.base16Scheme = "${pkgs.base16-schemes}/share/themes/gruvbox-light-hard.yaml";
   ```

2. **更换壁纸**：
   ```nix
   stylix.image = /path/to/your/wallpaper.png;
   ```

3. **更换字体**：
   ```nix
   stylix.fonts.monospace = {
     package = pkgs.fira-code;
     name = "Fira Code";
   };
   ```

### 支持的应用
目前配置了以下应用的主题支持：
- ✅ Neovim (通过 nixvim)
- ✅ Yazi (文件管理器)
- ✅ Fzf (模糊查找器)
- ✅ Starship (命令行提示符)

### 添加更多应用
如果想要为其他应用启用主题，编辑 `nix/modules/darwin/stylix.nix`：

```nix
stylix.targets = {
  neovim.enable = true;
  yazi.enable = true;
  fzf.enable = true;
  starship.enable = true;
  # 添加新的应用
  bat.enable = true;
  helix.enable = true;
};
```

## 架构优势

### 正确的模块分层
- **系统模块** (`nix/modules/darwin/`): 系统级配置，影响所有用户
- **Home Manager** (`nix/home/<USER>
- **清晰职责**: 避免配置冲突，更好的可维护性

### Home Manager 集成
- 通过 `stylix.homeManagerIntegration` 自动配置用户环境
- 无需在每个用户的 Home Manager 配置中重复设置
- 支持用户自定义覆盖系统主题

## 注意事项

1. **首次构建**：需要下载 Stylix 及其依赖，可能需要一些时间
2. **网络问题**：如果遇到网络问题，可以尝试：
   ```bash
   nix-store --optimise
   nix-collect-garbage -d
   ```
3. **字体安装**：确保系统中安装了配置的字体包
4. **兼容性**：某些应用可能需要重新启动才能应用新的主题

## 下一步建议

1. 测试配置是否正常工作
2. 根据个人喜好调整主题和颜色
3. 探索更多 Stylix 支持的应用
4. 考虑添加自定义壁纸和字体

## 故障排除

如果遇到问题：
1. 检查 `darwin-rebuild switch` 的输出
2. 查看 Home Manager 日志：`home-manager generations`
3. 尝试删除并重新生成配置
4. 确保 Stylix 输入正确添加到 flake 中
5. 检查 Darwin 模块是否正确导入 Stylix 配置