---
version: '3'

tasks:
  # Process management tasks
  ps:
    desc: Report a snapshot of current processes
    cmd: ps aux

  kill-port:
    desc: Kill processes running on a specific port
    cmd: lsof -t -i :{{.port}} | xargs kill
    vars:
      port: "{{.port | default 8080}}"

  # Network diagnostic tasks
  dig:
    desc: DNS lookup utility
    cmd: dig {{.domain}}
    vars:
      domain: "{{.domain}}"

  # File operations tasks
  create-file:
    desc: Create or overwrite a file with content
    cmd: echo "{{.content}}" > {{.filename}}
    vars:
      content: '{{.content}}'
      filename: '{{.filename | default "output.txt"}}'


  append-file:
    desc: Append content to a file
    cmd: echo "{{.content}}" >> {{.filename}}
    vars:
      content: '{{.content | default ""}}'
      filename: '{{.filename | default "output.txt"}}'


  clear-file:
    desc: Clear the contents of a file
    cmd: cat /dev/null > {{.filename}}
    vars:
      filename: '{{.filename | default "output.txt"}}'

  top-words:
    desc: Show the top 10 most frequent words in a file
    cmd: cat {{.filename}} | sort | uniq -c | sort -k1,1nr | head -10
    vars:
      filename: '{{.filename | default "output.txt"}}'

  # System performance monitoring tasks
  sar-cpu:
    desc: Monitor CPU utilization
    cmd: sar -u {{.interval}} {{.count}}
    vars:
      interval: '{{.interval | default "1"}}'
      count: '{{.count | default "5"}}'

  sar-memory:
    desc: Monitor memory utilization
    cmd: sar -r {{.interval}} {{.count}}
    vars:
      interval: '{{.interval | default "1"}}'
      count: '{{.count | default "5"}}'

  sar-network:
    desc: Monitor network device statistics
    cmd: sar -n DEV {{.interval}} {{.count}}
    vars:
      interval: '{{.interval | default "1"}}'
      count: '{{.count | default "5"}}'

  whoami:
    desc: Print the current user ID
    cmd: whoami

  # - history - Show recent commands
  # - history -20 -1 # Display the last 20 commands
  # - history 1 20 # Display the first 20 commands
  # - history 1 # Display all commands (from first to last)
  # - history 10600 # Display from n to last
  # - history -E # With timestamp format # 1  25.8.2025 09:55  touch .zshrc
  # - history -i # With timestamp format #  1  2025-08-25 09:55  touch .zshrc
  history:
    cmd: history -i ｜ grep {{.str}}

  tree:
    cmd: tree -L 1
    dir: '{{.USER_WORKING_DIR}}'
    interactive: true

#- 如果某个url需要auth才能访问，怎么在curl中添加token? # curl -H "X-Auth-Token: " url
#- "***怎么在本地执行远程 vps 上的远程脚本（curl 方式执行 shell 脚本时如何传参（不具名参数和具名参数分别怎么传参））？***" # [通过命令下载执行恶意代码](https://juejin.cn/post/6950955375931686942) 由于直接调用了bash命令，因此在远程脚本需要传递具名参数时，为了区分是bash命令的参数还是远程脚本的，可以使用--作为区分，可以理解为分割线，--前面的比如-s属于bash，后面的-x abc -y xyz属于远程脚本的参数. curl -L <url> | bash -s -- -x abc -y xyz
  curl:




  # Other commands that don't need individual tasks:
  # - alias - Create aliases for commands
  # - unalias - Remove aliases
  # - setenv - Set environment variables
  # - tar - Manipulate tar archives
  # - gzip - Compress/uncompress files
  # - zip - Package and compress files
  # - ln - Make links between files
  # - curl - Transfer data from or to a server
  # - transfer files (ffff, xxx, zzz) - File transfer utilities
  # - wget - Non-interactive network downloader
  # - rsync - Remote file synchronization
  # - scp - Secure copy files
  # - sort - Sort lines of text files
  # - uniq - Report or omit repeated lines
  # - awk - Pattern scanning and processing language
  # - read file (cat/head/tail/more/less) - File reading utilities

  # - systemctl list-unit-files | grep enable # View all auto-start items, or use systemctl list-unit-files | grep <service> to check if a specific service auto-starts

  # - open command:
  # - open -t <filename> # Open file with default editor
  # - open -e <filename> # Open file with "Text Editor"
  # - open -a <editor> <filename> # Open file with "specified application", e.g. open -a goland to open file with goland
  #
  # - Type command to check if a command is a built-in system command # type <command>
  #
  # - ip=$(ifconfig en0 | grep 'inet .*'  | sed 's/^.*inet//g' | sed 's/ netmask.*//g') && echo $ip
  # - echo -e "$(cmd)" # Use `echo -e`+double quotes to avoid special character issues (ensure special characters are not lost)
  # - echo -n # Echo without newline, use echo -n to prevent strings from being wrapped
  # - sed -n '1w <output-file>' <input-file> # Write the first line of input-file to output-file
