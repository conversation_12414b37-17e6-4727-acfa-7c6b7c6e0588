---
version: '3'


vars:
  GALAXY_CLI: ansible-galaxy

#- ansible all -m ping -u bruce # 检查所有的远程主机，是否以 bruce 用户创建了 ansible 主机可以访问的环境
#- ansible all -a "/bin/echo hello" # 在所有的远程主机上，以当前 bash 的同名用户，在远程主机执行“echo bash”
#- ansible all -m user -a "name=foo password=<crypted password here>" # 添加用户
#- ansible web -m service -a "name=httpd state=started" # 启动服务
#- ansible web -m git -a "repo=git://..." # 下载 git 包
#- ansible lb -a "/sbin/reboot" -f 10 # 并行执行：启动 10 个并行进行执行重启
#- ansible all -m setup # 查看远程主机的全部系统信息
#- ansible <xxx> --list-host # 查看组中的 host 清单
#- ansible-playbook -v playbook/centos.yml --list-hosts # 查看脚本影响到的 hosts
#- ansible-playbook --check playbook/centos.yml -i hosts # 预执行，查看 playbook 语法是否正确，以及在目标服务器上是否能够执行成功 (但是并不保证一定能够执行成功)
#- ansible-playbook -v playbook/centos.yml -i hosts # 执行并查看输出细节
#- ansible-playbook --check playbook/helloworld.yml -i hosts # 执行基础 playbook，确定 playbook 是否 work
#- ansible all -m command -a 'echo Hello World on Docker.' # 确保 ssh 能够连接所有目标服务器 (请一定确认 ssh 连接已建立，因为未建立 ssh 连接也会成功)







tasks:
  ping:
    cmds:
      - ansible {{.CLI_ARGS}} --list-host # 查看组中的host清单
      - ansible {{.CLI_ARGS}} -m ping -vvv # 确保ssh能够连接所有目标服务器(请一定确认ssh连接已建立，因为未建立ssh连接也会成功)
      - ansible {{.CLI_ARGS}} -a uptime
      - ansible {{.CLI_ARGS}} -m command -a 'echo Hello World on Docker.'
  #      - ansible rancher -a 'hostname' -vvv
  #      - ansible rancher --list
  #
  #      - ansible rancher -a 'docker ps -a'
  #
  #      - ansible rancher --check -m script -a './k8s-install.sh' -i hosts
  #      - ansible rancher -m script -a './k8s-install.sh' -i hosts
  #      - ansible rancher -m script -a './k8s-install.sh'


  centos:
    cmds:
      - ansible-playbook -v playbook/centos.yml -i hosts # 执行并查看输出细节

  #      - # ansible-playbook -v playbook/helloworld.yml -i hosts
  #      - # ansible-playbook -v playbook/k8s.yml -i hosts
  #      - # ansible-playbook -v playbook/hk.yml -i hosts
  #      - ansible-playbook playbook/centos.yml -i hosts -l ip
  #      - ansible-playbook playbook/vps.yml -i hosts -l ip
  #
  #      - ansible-galaxy install gantsign.oh-my-zsh # 需要先fetch remote roles
  #
  #      # [Ansible Galaxy - samdoran.caddy](https://galaxy.ansible.com/ui/standalone/roles/samdoran/caddy/)
  #      - ansible-galaxy role install samdoran.caddy
  #

  #      # [Ansible Galaxy - fesaille.gh](https://galaxy.ansible.com/ui/standalone/roles/fesaille/gh/)
  #      - ansible-galaxy role install fesaille.gh
  #
  #      - ansible asb -m copy -a "src=/home/<USER>/playbook/pb_shell.sh dest=/home/<USER>/playbook/" # 把文件cp到目标机器
  #    preconditions:
  #      - ansible-playbook -v playbook/centos.yml --list-hosts # 查看脚本影响到的hosts
  #      - ansible-playbook --check playbook/centos.yml -i hosts # 预执行，查看playbook语法是否正确，以及在目标服务器上是否能够执行成功;(但是并不保证一定能够执行成功)

  #  search
  #  install
  #  collection install	安装集合（如Docker管理）	ansible-galaxy collection install community.docker --force
  #  list	列出已安装的角色/集合	ansible-galaxy list
  #  init	初始化新角色目录结构	ansible-galaxy init my_role
  #  remove	删除本地角色/集合	ansible-galaxy remove geerlingguy.nginx
  #  info	查看角色/集合详情	ansible-galaxy info community.general
  #  install -r	按文件批量安装	ansible-galaxy install -r requirements.yml




  # [chusiang/ansible-managed-node.dockerfile: Building the Docker image with run the OpenSSH daemon and Python for Ansible.](https://github.com/chusiang/ansible-managed-node.dockerfile) 用来在本地玩ansible
  setup:
    cmd: docker run --name server1 -d -P chusiang/ansible-managed-node:centos-7
