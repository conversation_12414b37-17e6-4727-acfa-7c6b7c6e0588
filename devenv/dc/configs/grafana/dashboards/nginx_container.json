{"id": null, "title": "<PERSON><PERSON><PERSON>", "description": "Nginx exporter metrics", "tags": ["nginx"], "style": "dark", "timezone": "browser", "editable": true, "hideControls": false, "sharedCrosshair": true, "rows": [{"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 3, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(nginx_connections_processed_total{stage=\"any\"}[5m])) by (stage)", "hide": false, "interval": "", "intervalFactor": 10, "legendFormat": "requests", "metric": "", "refId": "B", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Requests/sec", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 2, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(nginx_connections_current) by (state)", "interval": "", "intervalFactor": 2, "legendFormat": "{{state}}", "metric": "", "refId": "A", "step": 2}], "timeFrom": null, "timeShift": null, "title": "Connections", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 1, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(nginx_connections_processed_total{stage!=\"any\"}[5m])) by (stage)", "hide": false, "interval": "", "intervalFactor": 10, "legendFormat": "{{stage}}", "metric": "", "refId": "B", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Connections rate", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "title": "Nginx exporter metrics"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": null, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 4, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{name=~\"nginx\"}[5m])) / count(node_cpu_seconds_total{mode=\"system\"}) * 100", "intervalFactor": 2, "legendFormat": "nginx", "refId": "A", "step": 2}], "timeFrom": null, "timeShift": null, "title": "CPU usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "title": "Nginx container metrics"}], "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "10s", "schemaVersion": 12, "version": 9, "links": [], "gnetId": null}