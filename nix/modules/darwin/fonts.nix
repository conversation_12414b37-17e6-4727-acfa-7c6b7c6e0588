# Darwin fonts configuration
# Font management and configuration for macOS
{pkgs, ...}: {
  # Fonts configuration
  fonts = {
    # Enable font directory
    fontDir.enable = true;

    # Install fonts
    fonts = with pkgs; [
      # Programming fonts
      jetbrains-mono
      fira-code
      fira-code-symbols
      ibm-plex
      source-code-pro
      cascadia-code

      # Nerd fonts
      nerd-fonts.jetbrains-mono
      nerd-fonts.fira-code
      nerd-fonts.source-code-pro
      nerd-fonts.caskaydia-cove
      nerd-fonts.meslo-lg

      # General purpose fonts
      noto-fonts
      noto-fonts-cjk
      noto-fonts-cjk-sans
      noto-fonts-cjk-serif
      noto-fonts-emoji
      noto-fonts-extra

      # Apple system fonts (SF Pro, SF Mono, etc.)
      sf-pro
      sf-mono

      # Icon fonts
      material-design-icons
      material-icons

      # Emoji fonts
      noto-fonts-color-emoji
      twitter-color-emoji
      openmoji-color

      # Additional fonts
      liberation_ttf
      dejavu_fonts
      corefonts
      vistafonts

      # Chinese fonts
      source-han-sans
      source-han-serif
      wqy_zenhei
      wqy_microhei
    ];

    # Font configuration
    fontconfig = {
      defaultFonts = {
        monospace = [
          "JetBrains Mono"
          "Fira Code"
          "SF Mono"
        ];
        sansSerif = [
          "SF Pro"
          "Noto Sans"
          "Inter"
        ];
        serif = [
          "SF Pro"
          "Noto Serif"
          "Source Han Serif"
        ];
        emoji = [
          "Apple Color Emoji"
          "Noto Color Emoji"
          "Twitter Color Emoji"
        ];
      };

      # Enable font antialiasing
      antialias = true;
      hinting = true;
      hinting.style = "slight";

      # Font subpixel rendering
      subpixel.rgba = "rgb";
      subpixel.lcdfilter = "default";

      # Enable automatic font discovery
      enable = true;
      includeUserConf = true;
    };
  };
}
