---
version: '3'


# [Advanced macOS Commands - saurabhs.org](https://saurabhs.org/advanced-macos-commands)
#- launchctl list # 查看自启的services和process(通常 | grep来搜索 想要查找的计划任务)
#- pmset -g # 打印所有可用的电源配置信息
#- pmset -g | grep hibernatemode # hibernate(睡眠)和sleep(休眠)的区别在于内存是否供电，睡眠状态内存不供电不工作，内存数据落盘，休眠则内存继续工作；唤醒方式也不同，睡眠模式需要电源键启动，休眠模式通过“键鼠等输入设备”即可唤醒）。0，掉电非常严重；3，掉电一般严重；25，那掉电就会更少一些。
#- pmset repeat wakeorpoweron MTWRFSU 5:00:00 shutdown MTWRFSU 22:00:00 # pmset [repeat, schedule] [sleep, wake, poweron, shutdown, wakeorpoweron] [<MTWRFSU> <date/time>]
#- pmset -g sched # 查看crontab的电源计划
#- pmset displaysleepnow # 立即使显示器进入睡眠状态，而不使系统的其余部分进入睡眠状态
#- pmset sleepnow # 立即使整个系统进入睡眠状态
#- pmset -a disablesleep 1 # 作用为防止系统休眠；注意，输入这条指令后MacBook不会进入休眠状态
#- pmset -a disablesleep 0 # 恢复，上面命令的相反
#- caffeinate, # Running caffeinate with no flags or arguments prevents your Mac from going to sleep as long as the command continues to run.
#
#- pbcopy
#- pbpaste
#- sips
#- textutil
#- mdfind
#- mdls
#- screencapture
#- taskpolicy
#- say # announces the given message, just for English.
#- networksetup


tasks:
  # [fastfetch-cli/fastfetch: A maintained, feature-rich and performance oriented, neofetch like system information tool.](https://github.com/fastfetch-cli/fastfetch) 因为neofetch的性能差（因为基于bash实现）、并且已经EOL了，所以选择 fastfetch
  fetch:
    cmd: fastfetch


  df:
    cmd: df -lh # disk free

  du:
    cmd: du -sh * | sort -h # disk usage, 查看文件夹下各文件大小，并排序
    dir: '{{.USER_WORKING_DIR}}'


  hostname:
    interactive: true
    silent: true
    desc: macos不同于其他linux distro，macos设计了三个名称，是为了在不同场景和应用层级中（用户界面、本地网络发现、系统网络配置）提供更精细和合适的标识
    cmds:
      - echo "【电脑名称】这是系统偏好设置中显示的名称，也是隔空投送等功能中他人看到的名称。核心标识。" # 这是给你看的。它是一个用户友好的、可读性高的名称，会显示在“关于本机”、共享设置、隔空投送（AirDrop）等图形界面中。你可以把它理解为这台电脑的“昵称”或“常用名”。
      - scutil --get ComputerName
      - echo "【主机名】主要用于系统内部和网络标识，初始默认为空或 localhost" # 这是给专业系统和网络服务看的。它是由管理员设置的、系统级的完整主机名（FQDN, Fully Qualified Domain Name），通常包含域名部分（如 myhost.example.com）。它可以被诸如远程登录（SSH）、网络文件系统（NFS）等许多底层 Unix 工具和网络服务使用。你的系统显示“未设置”，意味着 macOS 目前没有设定一个独立的、系统级的主机名，而是可能回退使用 LocalHostName 或其他名称。
      - scutil --get HostName || true
      - echo "【当前主机名】若 HostName 未设置，此命令可能返回 localhost"
      - hostname
      - echo "【本地主机名】这是用于本地 Bonjour 网络发现的名称，需配置 mDNS (如 Avahi)，通常以 .local 结尾" # 这是给本地网络上的其他 Apple 设备看的。它专门用于 Bonjour（mDNS）网络发现协议。Bonjour 允许你的 Mac 和其他苹果设备（如 iPhone、iPad、MacBook）在同一个局域网内无需配置就能相互发现和识别。你看到的 .local后缀就是 Bonjour 的典型特征。系统通常会自动从 ComputerName 生成一个合法的 LocalHostName（去除空格和特殊字符）。
      - scutil --get LocalHostName



    #  - url: https://github.com/syncthing/syncthing
    #    des: 一个基于P2P实现的“远程同步文件”工具，提供GUI和CLI（通过web操作）两种下载方式，用homebrew安装，默认CLI。用这个就可以代替之前用的【坚果云】了 (Some time ago used Nutstore to sync code bidirectionally. I've also used other cloud service like icloud, dropbox, google-cloud to implement similar task.)。

    #- url: https://github.com/yeongpin/cursor-free-vip
    #  des: 目前可用的

  # [nicolargo/glances](https://github.com/nicolargo/glances)
  top:
    cmd: glances # 更易用的top/htop，还支持web模式。但是其核心是“轻量级monitor系统”，glances还分可以分为server模式和client模式，所有slave机器都需要安装glances的server，master开启client模式就可以收集所有slave的数据（类似prometheus这样的pull模式）。所以也可以理解为局域网下的prom（glances不适合在公网做monitor），感觉意思不大。

  # [network-quality/community: A place (wiki) to coordinate the activity of the community of people interested in the network-quality project.](https://github.com/network-quality/community)


  nq:
    desc: macos内置speedtest命令
    cmd: networkQuality


  # [新型真●一键激活JetBrains全家桶方式，不需要手动下载任何文件(懒人福利)！idea/win/linux/mac - 开发调优 - LINUX DO](https://linux.do/t/topic/694120)
  license:
