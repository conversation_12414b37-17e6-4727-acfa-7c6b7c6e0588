# Claude Code 自定义配置使用指南

## 概述

你的 Nix 配置中包含了多个自定义的 Claude Code 功能，包括专门的子代理和自定义命令。这些配置可以帮助你更高效地进行开发工作。

## 🤖 子代理 (Subagents)

子代理是专门的 AI 助手，每个都有特定的技能集和工具权限。

### 1. Code Review Agent (@code-reviewer)

**用途**: 专业的代码审查代理，专注于代码质量、安全性和可维护性。

**可用工具**:
- Read (读取文件)
- Edit (编辑文件)  
- Grep (搜索代码)

**使用场景**:
- 需要对代码进行质量检查
- 代码安全审查
- 代码可维护性分析
- 提交前的代码评审

**使用方法**:
```
请使用 @code-reviewer 来审查这段代码
```

### 2. Documentation Agent (@documentation)

**用途**: 技术文档编写助手，创建清晰、全面的文档。

**可用工具**:
- Read (读取文件)
- Write (写入文件)
- Edit (编辑文件)

**使用场景**:
- 编写 API 文档
- 创建用户手册
- 生成代码注释
- 更新项目文档

**使用方法**:
```
请使用 @documentation 来为这个功能编写文档
```

### 3. Pre-commit Agent (@pre-commit)

**用途**: 运行 pre-commit 检查，确保代码质量标准。

**可用工具**:
- Bash (执行命令)
- Read (读取文件)

**使用场景**:
- 文件修改后自动检查代码质量
- 提交前的质量验证
- 格式化和风格检查

**使用方法**:
```
请使用 @pre-commit 来检查我刚修改的代码
```

## 📋 自定义命令 (Commands)

### 1. CI 检查命令 (/ci)

**用途**: 使用 omnix 运行本地持续集成检查。

**语法**: `/ci`

**功能**:
- 运行完整的 CI 检查套件
- 构建所有 flake 输出
- 执行测试、格式检查、验证等

**注意事项**:
- 这是一个耗时的操作，只在必要时使用
- 需要在 flake 项目目录中运行
- 需要安装 omnix (`om`)

**示例**:
```
/ci
```

### 2. GitHub Issue 实现命令 (/hack)

**用途**: 端到端实现 GitHub issue，确保所有 CI 检查通过。

**语法**: `/hack <github-issue-url> [plan_mode]`

**参数**:
- `issue_url` (必需): GitHub issue URL
- `plan_mode` (可选): 是否使用计划模式 (true/false，默认为 false)

**功能**:
1. 获取 GitHub issue 详情
2. 分析需求并创建实现计划
3. 实现功能或修复
4. 创建带有 issue 引用的提交
5. 运行 CI 检查
6. 修复 CI 问题直到所有检查通过

**前置条件**:
- GitHub CLI (`gh`) 已认证
- 在非主分支的 git 仓库中
- 安装了 omnix (`om`)
- 项目配置了 omnix CI

**示例**:
```bash
# 直接实现 issue
/hack https://github.com/myorg/myproject/issues/42

# 先创建计划，等待批准后实现
/hack https://github.com/myorg/myproject/issues/42 true
```

## 🚀 实际使用场景

### 场景 1: 代码开发流程

1. **获取任务**: 使用 `/hack` 命令实现 GitHub issue
   ```bash
   /hack https://github.com/yourorg/yourproject/issues/123
   ```

2. **代码审查**: 使用 `@code-reviewer` 审查实现
   ```
   请使用 @code-reviewer 来审查我的实现
   ```

3. **质量检查**: 使用 `@pre-commit` 运行检查
   ```
   请使用 @pre-commit 来检查代码质量
   ```

4. **CI 验证**: 运行 `/ci` 确保所有检查通过
   ```bash
   /ci
   ```

### 场景 2: 文档编写

1. **功能实现**: 开发新功能

2. **文档编写**: 使用 `@documentation` 编写文档
   ```
   请使用 @documentation 为我刚实现的功能编写用户文档
   ```

3. **代码注释**: 添加代码注释
   ```
   请使用 @documentation 为这个函数添加详细的注释
   ```

### 场景 3: 代码质量保证

1. **批量检查**: 使用 `@pre-commit` 检查整个项目
   ```
   请使用 @pre-commit 对整个项目运行质量检查
   ```

2. **安全审查**: 使用 `@code-reviewer` 进行安全检查
   ```
   请使用 @code-reviewer 重点审查安全性相关的代码
   ```

## ⚙️ 配置状态

### 当前生效的配置
- ✅ 环境变量配置正常
- ✅ 子代理配置已加载
- ✅ 自定义命令已配置
- ⚠️ MCP 服务器连接存在问题 (deepwiki 服务器返回 404)

### 已知限制
- deepwiki MCP 服务器当前不可用
- uvx 配置文件有问题，可能影响 nixos-mcp 服务

## 🛠️ 故障排除

### 如果子代理不工作
1. 确保在正确的目录中
2. 检查 Claude Code 是否正常运行
3. 重启 Claude Code 会话

### 如果自定义命令不工作
1. 验证语法是否正确
2. 检查前置条件是否满足
3. 查看错误信息并相应处理

### 如果 MCP 服务器不工作
1. 检查网络连接
2. 验证服务器 URL 是否正确
3. 检查相关工具是否已安装

## 📝 最佳实践

1. **按需使用**: 根据任务选择合适的代理或命令
2. **顺序使用**: 按照开发流程顺序使用各个工具
3. **质量优先**: 始终在提交前运行质量检查
4. **文档同步**: 功能完成后及时更新文档

---

*此指南基于你的 Nix 配置文件 `nix/home/<USER>