{"id": null, "title": "Docker Containers", "description": "Containers metrics", "tags": ["docker"], "style": "dark", "timezone": "browser", "editable": true, "hideControls": false, "sharedCrosshair": true, "rows": [{"collapse": false, "editable": true, "height": "150px", "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 4, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "sum(rate(container_cpu_user_seconds_total{image!=\"\"}[1m])) / count(node_cpu_seconds_total{mode=\"user\"}) * 100", "interval": "10s", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 10}], "thresholds": "65, 90", "title": "CPU Load", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 7, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "machine_cpu_cores", "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "machine_cpu_cores", "refId": "A", "step": 20}], "thresholds": "", "title": "CPU Cores", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 5, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "(sum(node_memory_MemTotal_bytes) - sum(node_memory_MemFree_bytes+node_memory_Buffers_bytes+node_memory_Cached_bytes) ) / sum(node_memory_MemTotal_bytes) * 100", "interval": "10s", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "thresholds": "65, 90", "title": "Memory Load", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 2, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "sum(container_memory_usage_bytes{image!=\"\"})", "interval": "10s", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "thresholds": "", "timeFrom": "10s", "title": "Used Memory", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": null, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 6, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "(node_filesystem_size_bytes{fstype=\"aufs\"} - node_filesystem_free_bytes{fstype=\"aufs\"}) / node_filesystem_size_bytes{fstype=\"aufs\"}  * 100", "interval": "30s", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 30}], "thresholds": "65, 90", "title": "Storage Load", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 3, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "sum(container_fs_usage_bytes)", "interval": "30s", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "Used Storage", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "timeFrom": "10s", "hideTimeOverride": true}], "title": "Overview"}, {"collapse": false, "editable": true, "height": "150px", "panels": [{"aliasColors": {}, "bars": true, "datasource": "Prometheus", "decimals": 0, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)", "thresholdLine": false}, "id": 9, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "scalar(count(container_memory_usage_bytes{image!=\"\"}) > 0)", "interval": "", "intervalFactor": 2, "legendFormat": "containers", "refId": "A", "step": 2}], "timeFrom": null, "timeShift": null, "title": "Running Containers", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": true, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 10, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "load 1m", "color": "#BF1B00"}], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "node_load1", "interval": "", "intervalFactor": 2, "legendFormat": "load 1m", "metric": "node_load1", "refId": "A", "step": 2}], "timeFrom": null, "timeShift": null, "title": "System Load", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 15, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "written", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(node_disk_read_bytes_total[5m]))", "interval": "2s", "intervalFactor": 4, "legendFormat": "read", "metric": "", "refId": "A", "step": 8}, {"expr": "sum(irate(node_disk_written_bytes_total[5m]))", "interval": "2s", "intervalFactor": 4, "legendFormat": "written", "metric": "", "refId": "B", "step": 8}, {"expr": "sum(irate(node_disk_io_time_seconds_total[5m]))", "interval": "2s", "intervalFactor": 4, "legendFormat": "io time", "metric": "", "refId": "C", "step": 8}], "timeFrom": null, "timeShift": null, "title": "I/O Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "title": "Host stats"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 8, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (rate(container_cpu_usage_seconds_total{image!=\"\"}[1m])) / scalar(count(node_cpu_seconds_total{mode=\"user\"})) * 100", "intervalFactor": 10, "legendFormat": "{{ name }}", "metric": "container_cpu_user_seconds_total", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Container CPU Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "CPU"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 11, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name)(container_memory_usage_bytes{image!=\"\"})", "intervalFactor": 1, "legendFormat": "{{ name }}", "metric": "container_memory_usage", "refId": "A", "step": 1}], "timeFrom": null, "timeShift": null, "title": "Container Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 12, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (container_memory_cache{image!=\"\"})", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "container_memory_cache", "refId": "A", "step": 2}], "timeFrom": null, "timeShift": null, "title": "Container Cached Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "Memory"}, {"collapse": false, "editable": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 13, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (rate(container_network_receive_bytes_total{image!=\"\"}[1m]))", "intervalFactor": 10, "legendFormat": "{{ name }}", "metric": "container_network_receive_bytes_total", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Container Network Input", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {"threshold1": null, "threshold1Color": "rgba(216, 200, 27, 0.27)", "threshold2": null, "threshold2Color": "rgba(234, 112, 112, 0.22)"}, "id": 14, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (rate(container_network_transmit_bytes_total{image!=\"\"}[1m]))", "intervalFactor": 10, "legendFormat": "{{ name }}", "metric": "container_network_transmit_bytes_total", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Container Network Output", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"show": true}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "title": "Network"}], "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "10s", "schemaVersion": 12, "version": 8, "links": [], "gnetId": null}