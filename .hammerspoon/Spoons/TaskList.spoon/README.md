# Hammerspoon 多任务管理器

一个功能丰富的 Hammerspoon 任务管理器，支持优先级、倒计时、自动评分等功能。

## 功能特性

### 基础功能
- ✅ 任务的增删改查
- ✅ 任务优先级（高/中/低，用红/绿/蓝色标识）
- ✅ 任务日期管理（默认今天，可自定义）
- ✅ 预计耗时设置（E1f = 40分钟）
- ✅ 实时倒计时显示
- ✅ 计时器暂停/恢复功能

### 高级功能
- ✅ 智能任务排序（按日期+优先级）
- ✅ 逻辑删除（完成任务）vs 真删除
- ✅ 自动评分系统（1-5分）
- ✅ 导出已完成任务（YAML格式）
- ✅ 数据持久化存储

## 使用方法

### 安装
1. 将 `hammerspoon_task_manager.lua` 复制到你的 Hammerspoon 配置目录
2. 在 `init.lua` 中添加：`require("hammerspoon_task_manager")`
3. 重新加载 Hammerspoon 配置

### 基本操作

#### 添加任务
- 点击菜单栏图标快速添加
- 或通过菜单选择"➕ 添加新任务"
- 分步填写：任务名称 → 日期 → 预计耗时

#### 管理任务
- 右键任务可以：选为当前任务、编辑、完成、删除
- 当前任务会显示倒计时
- 可以暂停/恢复计时器（菜单栏显示 ▶️ 运行中 / ⏸ 已暂停）
- 可以为当前任务添加实际耗时（AD）

#### 导出功能
- 选择"📤 导出已完成任务"
- 输入日期，自动生成 YAML 格式
- 结果复制到剪贴板

## 数据结构

每个任务包含以下字段：
```lua
{
    name = "任务名称",           -- 必填
    date = "2025-06-28",        -- 执行日期
    priority = 3,               -- 优先级 (3=高, 2=中, 1=低)
    estimatedTime = 2,          -- 预计耗时 (几个E1f)
    actualTime = 75,            -- 实际耗时 (分钟)
    isDone = false,             -- 是否完成
    doneAt = "2025-06-28 14:30", -- 完成时间
    startTime = 1640995200      -- 开始时间戳
}
```

## 评分算法

自动评分基于以下因素：
- **时间效率**：实际耗时 vs 预计耗时的比值
  - ≤80%：+1.5分（提前完成）
  - ≤100%：+0.5分（按时完成）
  - ≤120%：-0.5分（轻微超时）
  - >120%：-1.5分（严重超时）
- **优先级加成**：
  - 高优先级：+0.5分
  - 低优先级：-0.3分
- **基础分**：3分
- **最终分数**：1-5分（四舍五入）

## 导出格式示例

```yaml
- date: 2025-06-28
  task:
    - name: 优化hammerspoon脚本
      doneAt: 2025-06-28 12:20
      PD: 80min
      AD: 75min
      score: 4
    - name: 处理邮件
      doneAt: 2025-06-28 16:10
      PD: 40min
      AD: 35min
      score: 5
```

## 快捷操作

- **点击菜单栏图标**：快速添加任务
- **菜单栏显示**：当前任务 + 倒计时 + 优先级标识
  - ▶️ 表示计时器运行中
  - ⏸ 表示计时器已暂停
- **任务排序**：自动按日期和优先级排序
- **计时管理**：自动开始计时，支持暂停恢复
- **时间管理**：
  - PD（预计耗时）：在创建/编辑任务时设置
  - AD（实际耗时）：通过"添加实际耗时"功能手动添加
- **简化设计**：移除了优先级功能，专注于时间管理

## 注意事项

1. **分步对话框**：添加和编辑任务使用分步对话框，操作简单清晰
2. **数据备份**：任务数据保存在 `~/.hammerspoon/tasks_data.json`
3. **时间单位**：E1f = 40分钟，可根据需要调整
4. **最大任务数**：默认20个活跃任务，可修改 `maxTasks` 变量
5. **简化设计**：移除了优先级功能，专注于时间管理

## 故障排除

如果遇到问题：
1. 检查 Hammerspoon 控制台是否有错误信息
2. 确认 `tasks_data.json` 文件权限正常
3. 重新加载 Hammerspoon 配置
4. 如果数据损坏，删除 `tasks_data.json` 重新开始
