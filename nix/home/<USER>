# builtins.readFile vs 文件路径引用 - 时机分析

## 🕐 关键发现：时序问题确实存在！

你的怀疑是正确的！`builtins.readFile` 确实会有时序问题：

### 🔍 当前时序流程

1. **nix-darwin switch** 开始
2. **Nix 评估阶段** - `builtins.readFile` 试图读取 `/etc/claude/zai/token`
3. **⚠️ 问题**: 文件还不存在，评估失败！
4. **sops-nix 解密** - 才会创建密钥文件
5. **系统激活** - 设置文件权限等

---

## 📊 对比分析

### 方案 A: builtins.readFile (当前方法)
```nix
ANTHROPIC_AUTH_TOKEN = builtins.readFile /etc/claude/zai/token;
```

**问题:**
- ❌ 评估时文件不存在
- ❌ 第一次 switch 会失败
- ❌ 需要手动干预

### 方案 B: 文件路径引用 (rclone 模式)
```nix
# 像 rclone 那样处理
programs.rclone = {
  remotes.r2.secrets = {
    access_key_id = "/etc/rclone/r2/access_key_id";
    secret_access_key = "/etc/rclone/r2/secret_access_key";
  };
};
```

**优势:**
- ✅ Nix 只存储路径，不读取内容
- ✅ 程序运行时读取文件
- ✅ 文件存在时才访问

### 方案 C: 环境变量路径引用
```nix
# 更好的方式
home.sessionVariables = {
  ANTHROPIC_AUTH_TOKEN = "/etc/claude/zai/token";  # 路径，不是内容
};
```

---

## 🎯 推荐解决方案

### 最佳方案: 使用文件路径
```nix
# 在 cc.nix 中
home.sessionVariables = {
  ANTHROPIC_BASE_URL = "https://open.bigmodel.cn/api/anthropic";
  ANTHROPIC_AUTH_TOKEN = "/etc/claude/zai/token";  # 只存储路径
};
```

### 如果 Claude CLI 需要直接内容
创建一个包装脚本：
```nix
# 在 cc.nix 中
home.packages = [
  (pkgs.writeShellScriptBin "claude-wrapper" ''
    export ANTHROPIC_BASE_URL="https://open.bigmodel.cn/api/anthropic"
    export ANTHROPIC_AUTH_TOKEN="$(cat /etc/claude/zai/token)"
    exec ${pkgs.claude-code}/bin/claude "$@"
  '')
];
```

---

## 🔍 为什么 rclone 能工作

查看你的 `rclone.nix` 配置：
```nix
secrets = {
  access_key_id = "/etc/rclone/r2/access_key_id";    # 路径！
  secret_access_key = "/etc/rclone/r2/secret_access_key";  # 路径！
};
```

rclone 程序在**运行时**读取这些文件，而不是 Nix 评估时。

---

## 🏆 结论

**你的直觉完全正确！** `builtins.readFile` 确实会导致第一次 switch 失败。

**推荐采用 rclone 模式：**
1. ✅ 存储文件路径而不是内容
2. ✅ 程序运行时读取文件
3. ✅ 避免时序问题
4. ✅ 更符合 Nix 声明式理念