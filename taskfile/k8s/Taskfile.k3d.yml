---
version: '3'

output: prefixed
silent: true

vars:
  CLUSTER_NAME: argo-cd
  CONTEXT_NAME: "k3d-{{.CLUSTER_NAME}}"
  KUBECTL: "kubectl --context={{.CONTEXT_NAME}}"
  KUBEAPPLY: "{{.KUBECTL}} apply"
  KUSTOMIZE: "{{.KUBEAPPLY}} --kustomize"
  KUBEWAIT: "{{.KUBECTL}} wait"
  KUBEWAIT_AVAIL: "{{.KUBEWAIT}} --for=condition=available"
  KUBEWAIT_READY: "{{.KUBEWAIT}} --for=condition=ready"
  KUBECREATE: "{{.KUBECTL}} create -o yaml --dry-run=client"

  TODAY: '{{ now | date "2006-01-02T15:04:05-07:00" }}'
  BLACK: \033[:0;30m
  RED: \033[:0;31m
  GREEN: \033[:0;32m
  ORANGE: \033[:0;33m
  BLUE: \033[:0;34m
  PURPLE: \033[:0;35m
  CYAN: \033[:0;36m
  LIGHT_GRAY: \033[:0;37m
  DARK_GRAY: \033[:1;30m
  LIGHT_RED: \033[:1;31m
  LIGHT_GREEN: \033[:1;32m
  YELLOW: \033[:1;33m
  LIGHT_BLUE: \033[:1;34m
  LIGHT_PURPLE: \033[:1;35m
  LIGHT_CYAN: \033[:1;36m
  WHITE: \033[:1;37m
  NOCOLOR: \u001b[0m
  REVERSED: \u001b[7m

tasks:
  # Commenting out default task to prevent recursion when included
  # default:
  #   prefix: ⚙️
  #   cmds:
  #     - task -l
  #   silent: true

  create:
    prefix: ⚙️ > create
    desc: create k3d cluster
    cmds:
      - k3d cluster create --config=cluster/config.yaml

  create:dev:
    prefix: ⚙️ > create
    desc: create k3d cluster (devcontainers)
    deps:
      - k3d:create
    cmds:
      - sed -i -e "s/0.0.0.0/host.docker.internal/g" ${HOME}/.kube/config

  destroy:
    prefix: ⚙️ > destroy
    desc: destroy k3d cluster
    cmds:
      - "k3d cluster delete {{.CLUSTER_NAME}}"

  start:
    prefix: ⚙️ > start
    desc: starts knative environment
    cmds:
      - "k3d cluster start {{.CLUSTER_NAME}}"

  stop:
    prefix: ⚙️ > stop
    desc: stop knative environment
    cmds:
      - "k3d cluster stop {{.CLUSTER_NAME}}"

  test:
    prefix: ⚙️ > test
    desc: test argo cd
    cmds:
      - "cd test && go mod tidy && go test -v"
